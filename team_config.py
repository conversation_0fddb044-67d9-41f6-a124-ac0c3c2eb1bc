#!/usr/bin/env python3
"""
Team Data Collection Configuration

Configure your team-specific data collection settings and file paths.
"""

import os
from datetime import datetime

class TeamConfig:
    def __init__(self):
        # Team Information
        self.TEAM_NAME = "Sappho Tea Force Tea"
        self.TEAM_ID = "sappho_tea_force_tea"
        self.TEAM_EMOJI = "📏"
        
        # Database Configuration
        self.DATABASE_PATH = "mmolb_analytics.db"
        
        # Data Collection Paths
        self.HTML_DIR = "mmolb html"
        self.OUTPUT_DIR = "collected_data"
        self.LOGS_DIR = "collection_logs"
        
        # File Patterns
        self.TEAM_PAGE_PATTERN = "team page*.html"
        self.PLAYER_PAGE_PATTERN = "player page*.htm"
        self.SCHEDULE_PAGE_PATTERN = "season schedule*.htm"
        self.GAME_PAGE_PATTERN = "game page*.htm"
        
        # Collection Settings
        self.COLLECT_OPPONENT_INTERNAL_DATA = False  # Only collect interaction data
        self.AUTO_BACKUP_DATABASE = True
        self.SAVE_RAW_HTML = True
        self.LOG_LEVEL = "INFO"  # DEBUG, INFO, WARNING, ERROR
        
        # Data Retention
        self.KEEP_LOGS_DAYS = 30
        self.BACKUP_RETENTION_DAYS = 90
        
        # Augment Tracking
        self.TRACK_AUGMENT_DECISIONS = True
        self.AUGMENT_DECISION_LOG = "augment_decisions.json"
        
        # Player Focus List (empty = collect all team players)
        self.FOCUS_PLAYERS = []  # e.g., ["Jamie McBride", "Player Name"]
        
        # Game Types to Collect
        self.COLLECT_REGULAR_SEASON = True
        self.COLLECT_PLAYOFFS = True
        self.COLLECT_EXHIBITION = False
        
        # Performance Tracking
        self.TRACK_PLAYER_PERFORMANCE = True
        self.TRACK_OPPONENT_MATCHUPS = True
        self.TRACK_WEATHER_IMPACT = True
        
    def get_data_sources(self):
        """Get current data sources based on available files."""
        sources = {}
        
        # Find team page
        team_files = self.find_files(self.TEAM_PAGE_PATTERN)
        if team_files:
            sources['team_page'] = team_files[0]  # Use most recent
        
        # Find player pages
        player_files = self.find_files(self.PLAYER_PAGE_PATTERN)
        if player_files:
            sources['player_pages'] = player_files
        
        # Find schedule page
        schedule_files = self.find_files(self.SCHEDULE_PAGE_PATTERN)
        if schedule_files:
            sources['schedule_page'] = schedule_files[0]  # Use most recent
        
        # Find game pages
        game_files = self.find_files(self.GAME_PAGE_PATTERN)
        if game_files:
            sources['game_pages'] = game_files
        
        return sources
    
    def find_files(self, pattern):
        """Find files matching pattern in HTML directory."""
        import glob
        
        search_pattern = os.path.join(self.HTML_DIR, pattern)
        files = glob.glob(search_pattern)
        
        # Sort by modification time (newest first)
        files.sort(key=lambda x: os.path.getmtime(x), reverse=True)
        
        return files
    
    def setup_directories(self):
        """Create necessary directories."""
        directories = [
            self.OUTPUT_DIR,
            self.LOGS_DIR,
            os.path.join(self.OUTPUT_DIR, "team"),
            os.path.join(self.OUTPUT_DIR, "players"),
            os.path.join(self.OUTPUT_DIR, "games"),
            os.path.join(self.OUTPUT_DIR, "schedules")
        ]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
    
    def get_backup_path(self):
        """Get database backup path."""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        return f"{self.DATABASE_PATH}.backup_{timestamp}"
    
    def should_collect_player(self, player_name):
        """Check if we should collect data for this player."""
        if not self.FOCUS_PLAYERS:
            return True  # Collect all if no focus list
        
        return player_name in self.FOCUS_PLAYERS
    
    def get_collection_summary(self):
        """Get summary of current collection configuration."""
        sources = self.get_data_sources()
        
        summary = {
            'team': self.TEAM_NAME,
            'database': self.DATABASE_PATH,
            'sources_found': {
                'team_pages': len(sources.get('team_page', [])) if isinstance(sources.get('team_page'), list) else (1 if sources.get('team_page') else 0),
                'player_pages': len(sources.get('player_pages', [])),
                'schedule_pages': len(sources.get('schedule_page', [])) if isinstance(sources.get('schedule_page'), list) else (1 if sources.get('schedule_page') else 0),
                'game_pages': len(sources.get('game_pages', []))
            },
            'settings': {
                'collect_opponent_data': self.COLLECT_OPPONENT_INTERNAL_DATA,
                'track_augments': self.TRACK_AUGMENT_DECISIONS,
                'focus_players': len(self.FOCUS_PLAYERS),
                'auto_backup': self.AUTO_BACKUP_DATABASE
            }
        }
        
        return summary

def print_config_summary():
    """Print current configuration summary."""
    config = TeamConfig()
    summary = config.get_collection_summary()
    
    print("🔧 Team Data Collection Configuration")
    print("=" * 40)
    print(f"Team: {summary['team']}")
    print(f"Database: {summary['database']}")
    print()
    print("📁 Data Sources Found:")
    for source, count in summary['sources_found'].items():
        status = "✅" if count > 0 else "❌"
        print(f"  {status} {source}: {count} files")
    print()
    print("⚙️ Collection Settings:")
    for setting, value in summary['settings'].items():
        print(f"  • {setting}: {value}")
    print()

if __name__ == "__main__":
    print_config_summary()
