#!/usr/bin/env python3
"""
MMOLB Team Data Collection Runner

Simple command-line interface to run automated data collection for your team.
"""

import os
import sys
import shutil
from datetime import datetime
from team_config import TeamConfig
from automated_team_collector import TeamDataCollector

def backup_database(config):
    """Create database backup before collection."""
    if not config.AUTO_BACKUP_DATABASE:
        return True
    
    if not os.path.exists(config.DATABASE_PATH):
        print("ℹ️ No existing database to backup")
        return True
    
    try:
        backup_path = config.get_backup_path()
        shutil.copy2(config.DATABASE_PATH, backup_path)
        print(f"💾 Database backed up to: {backup_path}")
        return True
    except Exception as e:
        print(f"❌ Failed to backup database: {e}")
        return False

def check_data_sources(config):
    """Check what data sources are available."""
    sources = config.get_data_sources()
    
    print("🔍 Checking for data sources...")
    
    if not sources:
        print("❌ No data sources found!")
        print(f"   Please add HTML files to: {config.HTML_DIR}/")
        return False
    
    print("📁 Found data sources:")
    for source_type, files in sources.items():
        if isinstance(files, list):
            print(f"  • {source_type}: {len(files)} files")
            for file in files[:3]:  # Show first 3
                print(f"    - {os.path.basename(file)}")
            if len(files) > 3:
                print(f"    ... and {len(files) - 3} more")
        else:
            print(f"  • {source_type}: {os.path.basename(files)}")
    
    return True

def run_collection(config, sources):
    """Run the data collection process."""
    print("\n🚀 Starting Team Data Collection")
    print("=" * 40)
    
    # Initialize collector
    collector = TeamDataCollector(config.TEAM_NAME, config.DATABASE_PATH)
    
    # Run collection
    success = collector.run_full_collection(sources)
    
    if success:
        print("\n✅ Data collection completed successfully!")
        
        # Show collection summary
        print("\n📊 Collection Summary:")
        for log_entry in collector.collection_log:
            print(f"  • {log_entry['source']}: {log_entry['records_collected']} records")
        
        return True
    else:
        print("\n❌ Data collection encountered errors")
        return False

def show_database_summary(config):
    """Show summary of data in database."""
    try:
        import sqlite3
        
        conn = sqlite3.connect(config.DATABASE_PATH)
        cursor = conn.cursor()
        
        print("\n📈 Database Summary:")
        print("-" * 25)
        
        # Count records
        tables = ['teams', 'players', 'games', 'player_augments', 'plays']
        for table in tables:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"  {table}: {count} records")
            except:
                print(f"  {table}: table not found")
        
        # Show your team's recent games
        cursor.execute("""
            SELECT game_id, date, home_team_id, away_team_id, home_score, away_score
            FROM games 
            WHERE home_team_id = ? OR away_team_id = ?
            ORDER BY date DESC 
            LIMIT 5
        """, (config.TEAM_ID, config.TEAM_ID))
        
        recent_games = cursor.fetchall()
        if recent_games:
            print(f"\n🎮 Recent Games for {config.TEAM_NAME}:")
            for game in recent_games:
                home_team = game[2]
                away_team = game[3]
                score = f"{game[4]}-{game[5]}"
                date = game[1]
                
                if home_team == config.TEAM_ID:
                    opponent = away_team
                    result = "W" if game[4] > game[5] else "L"
                    location = "vs"
                else:
                    opponent = home_team
                    result = "W" if game[5] > game[4] else "L"
                    location = "@"
                
                print(f"  {result} {location} {opponent} {score} ({date})")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error reading database: {e}")

def main():
    """Main collection runner."""
    print("🏟️ MMOLB Team Data Collector")
    print("=" * 35)
    
    # Load configuration
    config = TeamConfig()
    config.setup_directories()
    
    # Show configuration
    print(f"Team: {config.TEAM_NAME}")
    print(f"Database: {config.DATABASE_PATH}")
    print()
    
    # Check for data sources
    if not check_data_sources(config):
        sys.exit(1)
    
    # Get data sources
    sources = config.get_data_sources()
    
    # Backup database
    if not backup_database(config):
        print("⚠️ Continuing without backup...")
    
    # Run collection
    success = run_collection(config, sources)
    
    if success:
        # Show database summary
        show_database_summary(config)
        
        print(f"\n🎉 Collection complete! Data saved to {config.DATABASE_PATH}")
        print(f"📝 Logs saved to {config.LOGS_DIR}/")
        
        # Suggest next steps
        print("\n💡 Next Steps:")
        print("  • Run 'python query_database.py' to view your data")
        print("  • Add more HTML files to continue collecting")
        print("  • Use the analysis tools to track performance")
        
    else:
        print("\n❌ Collection failed - check error messages above")
        sys.exit(1)

if __name__ == "__main__":
    main()
