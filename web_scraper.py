#!/usr/bin/env python3
"""
MMOLB Web Scraper

Automatically scrape MMOLB data directly from web URLs.
No need to manually save HTML files!
"""

import requests
from bs4 import BeautifulSoup
import time
import os
from datetime import datetime
from mmolb_scraper import <PERSON><PERSON><PERSON><PERSON><PERSON>rap<PERSON>
from automated_team_collector import TeamDataCollector
import json

class MMOLBWebScraper:
    def __init__(self, team_name="Sappho Tea Force Tea"):
        self.team_name = team_name
        self.team_id = team_name.lower().replace(' ', '_')
        self.scraper = MMOLBScraper()
        self.collector = TeamDataCollector(team_name)
        self.session = requests.Session()
        
        # Set up headers to mimic a real browser
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
        
        self.collection_log = []
    
    def fetch_page(self, url, page_type="unknown"):
        """Fetch a web page and return the HTML content."""
        try:
            print(f"🌐 Fetching {page_type} from: {url}")
            
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            
            print(f"✅ Successfully fetched {page_type} ({len(response.content)} bytes)")
            return response.text
            
        except requests.exceptions.RequestException as e:
            print(f"❌ Error fetching {url}: {e}")
            return None
        except Exception as e:
            print(f"❌ Unexpected error fetching {url}: {e}")
            return None
    
    def scrape_team_page(self, team_url):
        """Scrape team page from URL."""
        html_content = self.fetch_page(team_url, "team page")
        
        if not html_content:
            return None
        
        try:
            # Parse with BeautifulSoup
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # Extract team data using existing scraper
            team_data = self.scraper.extract_team_data(soup)
            
            if team_data:
                # Store in database
                self.collector.store_team_info(team_data['team_info'])
                self.collector.store_roster_data(team_data['roster'])
                self.collector.store_team_events(team_data['recent_events'])
                
                self.log_collection("team_url", len(team_data['roster']), len(team_data['recent_events']))
                
                # Save raw HTML for backup
                self.save_html_backup(html_content, "team_page", team_url)
                
                print(f"✅ Team data collected: {len(team_data['roster'])} players, {len(team_data['recent_events'])} events")
                return team_data
            else:
                print("❌ Failed to parse team data from URL")
                return None
                
        except Exception as e:
            print(f"❌ Error processing team page: {e}")
            return None
    
    def scrape_player_page(self, player_url):
        """Scrape player page from URL."""
        html_content = self.fetch_page(player_url, "player page")
        
        if not html_content:
            return None
        
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            player_data = self.scraper.extract_player_data(soup)
            
            if player_data:
                self.collector.store_player_details(player_data)
                
                player_name = player_data['player_info'].get('player_name', 'Unknown')
                self.log_collection("player_url", 1, 0)
                
                # Save raw HTML for backup
                self.save_html_backup(html_content, f"player_page_{player_name}", player_url)
                
                print(f"✅ Player data collected: {player_name}")
                return player_data
            else:
                print("❌ Failed to parse player data from URL")
                return None
                
        except Exception as e:
            print(f"❌ Error processing player page: {e}")
            return None
    
    def scrape_schedule_page(self, schedule_url):
        """Scrape schedule page from URL."""
        html_content = self.fetch_page(schedule_url, "schedule page")
        
        if not html_content:
            return None
        
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            schedule_data = self.scraper.extract_schedule_data(soup)
            
            if schedule_data:
                team_games = self.collector.filter_team_games(schedule_data['games'])
                self.collector.store_schedule_games(team_games)
                
                self.log_collection("schedule_url", len(team_games), 0)
                
                # Save raw HTML for backup
                self.save_html_backup(html_content, "schedule_page", schedule_url)
                
                print(f"✅ Schedule data collected: {len(team_games)} games")
                return schedule_data
            else:
                print("❌ Failed to parse schedule data from URL")
                return None
                
        except Exception as e:
            print(f"❌ Error processing schedule page: {e}")
            return None
    
    def scrape_game_page(self, game_url):
        """Scrape game page from URL."""
        html_content = self.fetch_page(game_url, "game page")
        
        if not html_content:
            return None
        
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            game_data = self.scraper.extract_game_data(soup)
            
            if game_data:
                # Check if it's a team game
                if self.collector.is_team_game(game_data):
                    self.collector.store_game_details(game_data, f"web_game_{int(time.time())}")
                    
                    self.log_collection("game_url", 1, len(game_data.get('play_by_play', [])))
                    
                    # Save raw HTML for backup
                    self.save_html_backup(html_content, "game_page", game_url)
                    
                    print(f"✅ Game data collected: {len(game_data.get('play_by_play', []))} plays")
                    return game_data
                else:
                    print("⏭️ Skipping non-team game")
                    return None
            else:
                print("❌ Failed to parse game data from URL")
                return None
                
        except Exception as e:
            print(f"❌ Error processing game page: {e}")
            return None
    
    def scrape_multiple_urls(self, urls_config):
        """Scrape multiple URLs based on configuration."""
        print("🚀 Starting Web-Based Data Collection")
        print("=" * 45)
        
        total_success = 0
        total_attempted = 0
        
        # Process team URLs
        if 'team_urls' in urls_config:
            for url in urls_config['team_urls']:
                total_attempted += 1
                if self.scrape_team_page(url):
                    total_success += 1
                time.sleep(2)  # Be polite to the server
        
        # Process player URLs
        if 'player_urls' in urls_config:
            for url in urls_config['player_urls']:
                total_attempted += 1
                if self.scrape_player_page(url):
                    total_success += 1
                time.sleep(2)
        
        # Process schedule URLs
        if 'schedule_urls' in urls_config:
            for url in urls_config['schedule_urls']:
                total_attempted += 1
                if self.scrape_schedule_page(url):
                    total_success += 1
                time.sleep(2)
        
        # Process game URLs
        if 'game_urls' in urls_config:
            for url in urls_config['game_urls']:
                total_attempted += 1
                if self.scrape_game_page(url):
                    total_success += 1
                time.sleep(2)
        
        # Save collection log
        self.save_collection_log()
        
        print(f"\n🎉 Web collection complete: {total_success}/{total_attempted} URLs successful")
        return total_success == total_attempted
    
    def save_html_backup(self, html_content, page_type, url):
        """Save HTML content as backup."""
        try:
            backup_dir = "html_backups"
            os.makedirs(backup_dir, exist_ok=True)
            
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"{page_type}_{timestamp}.html"
            filepath = os.path.join(backup_dir, filename)
            
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(f"<!-- Source URL: {url} -->\n")
                f.write(f"<!-- Scraped: {datetime.now().isoformat()} -->\n")
                f.write(html_content)
            
            print(f"💾 HTML backup saved: {filepath}")
            
        except Exception as e:
            print(f"⚠️ Failed to save HTML backup: {e}")
    
    def log_collection(self, source, records, events):
        """Log collection activity."""
        self.collection_log.append({
            'timestamp': datetime.now().isoformat(),
            'source': source,
            'records_collected': records,
            'events_collected': events
        })
    
    def save_collection_log(self):
        """Save collection log."""
        log_file = f"web_collection_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(log_file, 'w') as f:
            json.dump(self.collection_log, f, indent=2)
        print(f"📝 Web collection log saved: {log_file}")

def main():
    """Example usage of web scraper."""
    scraper = MMOLBWebScraper()
    
    # Example URLs configuration
    urls_config = {
        'team_urls': [
            # Add your team page URL here
            # 'https://mmolb.com/team/your-team-id'
        ],
        'player_urls': [
            # Add player page URLs here
            # 'https://mmolb.com/player/player-id'
        ],
        'schedule_urls': [
            # Add schedule page URLs here
            # 'https://mmolb.com/schedule/season-2'
        ],
        'game_urls': [
            # Add game page URLs here
            # 'https://mmolb.com/game/game-id'
        ]
    }
    
    print("🌐 MMOLB Web Scraper")
    print("=" * 25)
    print("To use this scraper:")
    print("1. Add your MMOLB URLs to the urls_config dictionary")
    print("2. Run the scraper")
    print("3. Data will be automatically collected and stored")
    print()
    print("Example URLs:")
    print("- Team: https://mmolb.com/team/your-team-id")
    print("- Player: https://mmolb.com/player/player-id") 
    print("- Schedule: https://mmolb.com/schedule/season-2")
    print("- Game: https://mmolb.com/game/game-id")
    print()
    
    if any(urls_config.values()):
        success = scraper.scrape_multiple_urls(urls_config)
        if success:
            print("✅ All URLs processed successfully!")
        else:
            print("⚠️ Some URLs failed - check logs for details")
    else:
        print("ℹ️ No URLs configured. Add URLs to urls_config to start scraping.")

if __name__ == "__main__":
    main()
