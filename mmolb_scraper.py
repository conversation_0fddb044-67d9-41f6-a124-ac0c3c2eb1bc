#!/usr/bin/env python3
"""
MMOLB Web Scraper

This script scrapes data from MMOLB team pages and extracts:
- Team information
- Player roster data
- Game results
- Recent events (augments, deliveries, etc.)
"""

import requests
from bs4 import BeautifulSoup
import re
import json
import csv
from datetime import datetime
import sqlite3
import os

class MMOLBScraper:
    def __init__(self, base_url="https://mmolb.com"):
        self.base_url = base_url
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
    
    def parse_html_file(self, file_path, page_type="team"):
        """Parse a local HTML file for testing purposes."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                html_content = f.read()

            soup = BeautifulSoup(html_content, 'html.parser')

            if page_type == "team":
                return self.extract_team_data(soup)
            elif page_type == "player":
                return self.extract_player_data(soup)
            elif page_type == "schedule":
                return self.extract_schedule_data(soup)
            elif page_type == "game":
                return self.extract_game_data(soup)
            else:
                print(f"Unknown page type: {page_type}")
                return None

        except Exception as e:
            print(f"Error parsing HTML file: {e}")
            return None
    
    def extract_team_data(self, soup):
        """Extract all team data from the parsed HTML."""
        data = {
            'team_info': self.extract_team_info(soup),
            'roster': self.extract_roster(soup),
            'recent_events': self.extract_recent_events(soup)
        }
        return data

    def extract_player_data(self, soup):
        """Extract all player data from the parsed HTML."""
        data = {
            'player_info': self.extract_player_info(soup),
            'player_profile': self.extract_player_profile(soup),
            'player_equipment': self.extract_player_equipment(soup),
            'player_stats': self.extract_player_stats(soup),
            'player_events': self.extract_player_events(soup)
        }
        return data
    
    def extract_team_info(self, soup):
        """Extract basic team information."""
        team_info = {}
        
        try:
            # Find the team header section
            team_header = soup.find('div', class_=lambda x: x and 'relative w-full h-28' in x)
            
            if team_header:
                # Team emoji
                emoji_span = team_header.find('span', class_='text-7xl')
                if emoji_span:
                    team_info['emoji'] = emoji_span.get_text().strip()
                
                # Team name - look for the larger text
                name_spans = team_header.find_all('span')
                for span in name_spans:
                    text = span.get_text().strip()
                    if len(text) > 5 and not text.isdigit() and '–' not in text:
                        if 'text-2xl' in span.get('class', []):
                            team_info['name'] = text
                            break
                
                # Team record and ranking
                record_spans = team_header.find_all('span', class_=lambda x: x and 'absolute' in x and 'opacity-80' in x)
                for span in record_spans:
                    text = span.get_text().strip()
                    if '–' in text:
                        team_info['record'] = text
                    elif text.isdigit():
                        team_info['ranking'] = int(text)
            
        except Exception as e:
            print(f"Error extracting team info: {e}")
        
        return team_info
    
    def extract_roster(self, soup):
        """Extract player roster information."""
        roster = []
        
        try:
            # Find roster section
            roster_container = soup.find('div', class_='w-128 space-y-2')
            
            if roster_container:
                player_divs = roster_container.find_all('div', class_=lambda x: x and 'flex justify-between items-center' in x)
                
                for player_div in player_divs:
                    player_data = {}
                    
                    # Extract player information
                    spans = player_div.find_all('span')
                    if len(spans) >= 4:
                        # Team emoji (spans[0])
                        player_data['team_emoji'] = spans[0].get_text().strip()
                        
                        # Jersey number (spans[1])
                        jersey_text = spans[1].get_text().strip()
                        if jersey_text.startswith('#'):
                            player_data['jersey_number'] = jersey_text[1:]
                        
                        # Position (spans[2])
                        player_data['position'] = spans[2].get_text().strip()
                        
                        # Player name (spans[3])
                        player_data['name'] = spans[3].get_text().strip()
                        
                        # Generate player ID
                        player_data['player_id'] = self.generate_player_id(player_data['name'])
                        
                        roster.append(player_data)
            
        except Exception as e:
            print(f"Error extracting roster: {e}")
        
        return roster
    
    def extract_recent_events(self, soup):
        """Extract recent events from the team page."""
        events = []
        
        try:
            # Find the recent events section
            events_container = soup.find('div', class_=lambda x: x and 'max-h-60 overflow-y-auto' in x)
            
            if events_container:
                event_divs = events_container.find_all('div')
                
                for event_div in event_divs:
                    event_text = event_div.get_text().strip()
                    if event_text:
                        event_data = self.parse_event_text(event_text)
                        if event_data:
                            events.append(event_data)
        
        except Exception as e:
            print(f"Error extracting recent events: {e}")
        
        return events
    
    def parse_event_text(self, event_text):
        """Parse individual event text to extract structured data."""
        event_data = {'raw_text': event_text}
        
        try:
            # Game results pattern
            game_pattern = r'⚾ Season (\d+), Regular Season, Day (\d+): (.+?) vs\. (.+?) - FINAL (\d+)-(\d+)'
            game_match = re.search(game_pattern, event_text)
            
            if game_match:
                event_data.update({
                    'type': 'game',
                    'season': int(game_match.group(1)),
                    'day': int(game_match.group(2)),
                    'home_team': game_match.group(3).strip(),
                    'away_team': game_match.group(4).strip(),
                    'home_score': int(game_match.group(5)),
                    'away_score': int(game_match.group(6))
                })
                return event_data
            
            # Stat gain pattern
            stat_pattern = r'🧩 Season (\d+), Regular Season, Day (\d+): (.+?) gained \+(\d+) (.+?)\.'
            stat_match = re.search(stat_pattern, event_text)
            
            if stat_match:
                event_data.update({
                    'type': 'stat_gain',
                    'season': int(stat_match.group(1)),
                    'day': int(stat_match.group(2)),
                    'player': stat_match.group(3).strip(),
                    'stat_increase': int(stat_match.group(4)),
                    'stat_name': stat_match.group(5).strip()
                })
                return event_data
            
            # Item delivery pattern
            delivery_pattern = r'📦 Season (\d+), Regular Season, Day (\d+): (.+?) received a (.+?) Delivery'
            delivery_match = re.search(delivery_pattern, event_text)
            
            if delivery_match:
                event_data.update({
                    'type': 'item_delivery',
                    'season': int(delivery_match.group(1)),
                    'day': int(delivery_match.group(2)),
                    'player': delivery_match.group(3).strip(),
                    'item': delivery_match.group(4).strip()
                })
                return event_data
            
            # Enchantment pattern
            enchant_pattern = r'🪄 Season (\d+), Regular Season, Day (\d+): .+? (.+?)\'s (.+?) was enchanted with \+(\d+) (.+?)\.'
            enchant_match = re.search(enchant_pattern, event_text)
            
            if enchant_match:
                event_data.update({
                    'type': 'enchantment',
                    'season': int(enchant_match.group(1)),
                    'day': int(enchant_match.group(2)),
                    'player': enchant_match.group(3).strip(),
                    'item': enchant_match.group(4).strip(),
                    'stat_increase': int(enchant_match.group(5)),
                    'stat_name': enchant_match.group(6).strip()
                })
                return event_data
            
            # If no pattern matches, return basic info
            season_day_pattern = r'Season (\d+), .+?, Day (\d+):'
            basic_match = re.search(season_day_pattern, event_text)
            
            if basic_match:
                event_data.update({
                    'type': 'other',
                    'season': int(basic_match.group(1)),
                    'day': int(basic_match.group(2))
                })
                return event_data
        
        except Exception as e:
            print(f"Error parsing event text: {e}")
        
        return event_data if 'type' in event_data else None
    
    def generate_player_id(self, player_name):
        """Generate a consistent player ID from player name."""
        # Remove special characters and convert to uppercase
        clean_name = re.sub(r'[^a-zA-Z\s]', '', player_name)
        # Take first letter of each word
        words = clean_name.split()
        if len(words) >= 2:
            return f"{words[0][:3]}{words[-1][:3]}".upper()
        else:
            return clean_name[:6].upper().replace(' ', '')
    
    def save_to_csv(self, data, output_dir="scraped_data"):
        """Save scraped data to CSV files."""
        os.makedirs(output_dir, exist_ok=True)
        
        # Save team info
        if data['team_info']:
            team_file = os.path.join(output_dir, 'team_info.csv')
            with open(team_file, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=data['team_info'].keys())
                writer.writeheader()
                writer.writerow(data['team_info'])
        
        # Save roster
        if data['roster']:
            roster_file = os.path.join(output_dir, 'roster.csv')
            with open(roster_file, 'w', newline='', encoding='utf-8') as f:
                fieldnames = ['player_id', 'name', 'jersey_number', 'position', 'team_emoji']
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(data['roster'])
        
        # Save events
        if data['recent_events']:
            events_file = os.path.join(output_dir, 'recent_events.csv')
            with open(events_file, 'w', newline='', encoding='utf-8') as f:
                # Get all possible fieldnames
                all_fields = set()
                for event in data['recent_events']:
                    all_fields.update(event.keys())
                
                writer = csv.DictWriter(f, fieldnames=sorted(all_fields))
                writer.writeheader()
                writer.writerows(data['recent_events'])
        
        print(f"Data saved to {output_dir}/")

    def extract_player_info(self, soup):
        """Extract basic player information from header."""
        player_info = {}

        try:
            # Find the player header section
            header = soup.find('div', class_=lambda x: x and 'relative w-full' in x and 'h-28' in x)

            if header:
                # Team name
                team_div = header.find('div', style=lambda x: x and 'font-size: 16.5px' in x)
                if team_div:
                    player_info['team_name'] = team_div.get_text().strip()

                # Player name
                name_div = header.find('div', style=lambda x: x and 'font-size: 32.8px' in x)
                if name_div:
                    player_info['player_name'] = name_div.get_text().strip()

                # Jersey number and position
                right_section = header.find('div', class_='text-right')
                if right_section:
                    divs = right_section.find_all('div')
                    if len(divs) >= 2:
                        jersey_text = divs[0].get_text().strip()
                        if jersey_text.startswith('#'):
                            player_info['jersey_number'] = jersey_text[1:]
                        player_info['position'] = divs[1].get_text().strip()

                # Team emoji
                emoji_div = header.find('div', class_='text-5xl')
                if emoji_div:
                    player_info['team_emoji'] = emoji_div.get_text().strip()

            # Health bar percentage
            health_bar = soup.find('div', style=lambda x: x and 'width:' in x and 'background-color: rgb(20, 204, 0)' in x)
            if health_bar:
                style = health_bar.get('style', '')
                width_match = re.search(r'width:\s*(\d+)%', style)
                if width_match:
                    player_info['health_percentage'] = int(width_match.group(1))

        except Exception as e:
            print(f"Error extracting player info: {e}")

        return player_info

    def extract_player_profile(self, soup):
        """Extract player profile information (born, home, likes, etc.)."""
        profile = {}

        try:
            # Find the grid with profile information - more flexible search
            grid = soup.find('div', class_=lambda x: x and 'grid' in x and 'grid-rows-2' in x and 'grid-flow-col' in x)

            if grid:
                # Look for profile sections with gray labels
                label_divs = grid.find_all('div', class_=lambda x: x and 'text-gray-400' in x)

                for label_div in label_divs:
                    label = label_div.get_text().strip().lower()
                    # Find the corresponding value div (next sibling or parent's next child)
                    parent = label_div.parent
                    if parent:
                        value_div = parent.find('div', class_=lambda x: x and 'font-bold' in x)
                        if value_div:
                            value = value_div.get_text().strip()
                            profile[label] = value

        except Exception as e:
            print(f"Error extracting player profile: {e}")

        return profile

    def extract_player_equipment(self, soup):
        """Extract player equipment/items."""
        equipment = []

        try:
            # Find equipment section - look for the flex container with items
            equipment_section = soup.find('div', class_=lambda x: x and 'flex' in x and 'justify-center' in x and 'flex-wrap' in x and 'gap-4' in x)

            if equipment_section:
                # Look for item containers
                item_divs = equipment_section.find_all('div', class_=lambda x: x and 'relative' in x and 'group' in x)

                for item_div in item_divs:
                    item_data = {}

                    # Item emoji - look for large text
                    emoji_div = item_div.find('div', class_=lambda x: x and 'text-3xl' in x)
                    if emoji_div:
                        item_data['emoji'] = emoji_div.get_text().strip()

                    # Item name - look for small font text
                    name_div = item_div.find('div', class_=lambda x: x and 'text-xs' in x and 'font-semibold' in x)
                    if name_div:
                        item_data['name'] = name_div.get_text().strip()

                    if item_data:
                        equipment.append(item_data)

        except Exception as e:
            print(f"Error extracting player equipment: {e}")

        return equipment

    def extract_player_stats(self, soup):
        """Extract comprehensive player statistics."""
        stats = {
            'batting': {},
            'pitching': {},
            'defense': {}
        }

        try:
            # Find the stats container - look for the main stats section
            stats_container = soup.find('div', class_=lambda x: x and 'bg-[#1c2a3a]' in x and 'p-6' in x and 'rounded-xl' in x)

            if stats_container:
                # Look for section headers and their corresponding grids
                section_headers = stats_container.find_all('div', class_=lambda x: x and 'text-lg' in x and 'font-semibold' in x)

                for header in section_headers:
                    section_name = header.get_text().strip().lower()

                    # Find the next grid after this header
                    next_element = header.find_next_sibling()
                    while next_element:
                        if next_element.name == 'div' and next_element.get('class'):
                            classes = ' '.join(next_element.get('class', []))
                            if 'grid' in classes and 'grid-cols-3' in classes:
                                stats[section_name] = self.extract_stat_grid(next_element)
                                break
                        next_element = next_element.find_next_sibling()

                # If the above doesn't work, try a more direct approach
                if not any(stats.values()):
                    # Look for all stat grids
                    stat_grids = stats_container.find_all('div', class_=lambda x: x and 'grid' in x and 'grid-cols-3' in x and 'gap-3' in x)

                    if len(stat_grids) >= 3:
                        stats['batting'] = self.extract_stat_grid(stat_grids[0])
                        stats['pitching'] = self.extract_stat_grid(stat_grids[1])
                        stats['defense'] = self.extract_stat_grid(stat_grids[2])

        except Exception as e:
            print(f"Error extracting player stats: {e}")

        return stats

    def extract_stat_grid(self, grid):
        """Extract statistics from a grid layout."""
        stats = {}

        try:
            # Look for stat containers - they might have different class patterns
            stat_divs = grid.find_all('div', class_=lambda x: x and 'relative' in x and 'group' in x)

            for stat_div in stat_divs:
                # Stat name - look for small bold text
                name_div = stat_div.find('div', class_=lambda x: x and 'text-sm' in x and 'font-bold' in x)
                # Stat value - look for base font text
                value_div = stat_div.find('div', class_=lambda x: x and 'text-base' in x and 'font-normal' in x)

                if name_div and value_div:
                    stat_name = name_div.get_text().strip()
                    stat_value = value_div.get_text().strip()

                    # Try to convert to number if possible
                    try:
                        if '.' in stat_value:
                            stat_value = float(stat_value)
                        elif stat_value.isdigit():
                            stat_value = int(stat_value)
                    except ValueError:
                        pass  # Keep as string if not a number

                    stats[stat_name] = stat_value

        except Exception as e:
            print(f"Error extracting stat grid: {e}")

        return stats

    def extract_player_events(self, soup):
        """Extract player-specific recent events."""
        events = []

        try:
            # Find the recent events section
            events_container = soup.find('div', class_=lambda x: x and 'max-h-60 overflow-y-auto' in x)

            if events_container:
                event_divs = events_container.find_all('div')

                for event_div in event_divs:
                    event_text = event_div.get_text().strip()
                    if event_text:
                        event_data = self.parse_event_text(event_text)
                        if event_data:
                            events.append(event_data)

        except Exception as e:
            print(f"Error extracting player events: {e}")

        return events

    def save_player_data_to_csv(self, data, output_dir="scraped_data"):
        """Save scraped player data to CSV files."""
        os.makedirs(output_dir, exist_ok=True)

        # Save player info
        if data['player_info']:
            player_file = os.path.join(output_dir, 'player_info.csv')
            with open(player_file, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=data['player_info'].keys())
                writer.writeheader()
                writer.writerow(data['player_info'])

        # Save player profile
        if data['player_profile']:
            profile_file = os.path.join(output_dir, 'player_profile.csv')
            with open(profile_file, 'w', newline='', encoding='utf-8') as f:
                writer = csv.DictWriter(f, fieldnames=data['player_profile'].keys())
                writer.writeheader()
                writer.writerow(data['player_profile'])

        # Save equipment
        if data['player_equipment']:
            equipment_file = os.path.join(output_dir, 'player_equipment.csv')
            with open(equipment_file, 'w', newline='', encoding='utf-8') as f:
                fieldnames = ['emoji', 'name']
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(data['player_equipment'])

        # Save stats (flatten the nested structure)
        if data['player_stats']:
            stats_file = os.path.join(output_dir, 'player_stats.csv')
            with open(stats_file, 'w', newline='', encoding='utf-8') as f:
                # Flatten stats structure
                flat_stats = {}
                for category, stats in data['player_stats'].items():
                    for stat_name, stat_value in stats.items():
                        flat_stats[f"{category}_{stat_name}"] = stat_value

                if flat_stats:
                    writer = csv.DictWriter(f, fieldnames=flat_stats.keys())
                    writer.writeheader()
                    writer.writerow(flat_stats)

        # Save events
        if data['player_events']:
            events_file = os.path.join(output_dir, 'player_events.csv')
            with open(events_file, 'w', newline='', encoding='utf-8') as f:
                # Get all possible fieldnames
                all_fields = set()
                for event in data['player_events']:
                    all_fields.update(event.keys())

                writer = csv.DictWriter(f, fieldnames=sorted(all_fields))
                writer.writeheader()
                writer.writerows(data['player_events'])

        print(f"Player data saved to {output_dir}/")

    def extract_schedule_data(self, soup):
        """Extract season schedule data from the parsed HTML."""
        data = {
            'schedule_info': self.extract_schedule_info(soup),
            'games': self.extract_schedule_games(soup)
        }
        return data

    def extract_schedule_info(self, soup):
        """Extract basic schedule information."""
        schedule_info = {}

        try:
            # Find the schedule title
            title = soup.find('h1', class_=lambda x: x and 'text-2xl' in x and 'font-bold' in x)
            if title:
                schedule_info['title'] = title.get_text().strip()

        except Exception as e:
            print(f"Error extracting schedule info: {e}")

        return schedule_info

    def extract_schedule_games(self, soup):
        """Extract individual games from the schedule grid."""
        games = []

        try:
            # Find the games grid
            grid = soup.find('div', class_=lambda x: x and 'grid' in x and 'gap-4' in x)

            if grid:
                # Find all game cards
                game_cards = grid.find_all('div', class_=lambda x: x and 'relative' in x and 'rounded-md' in x and 'cursor-pointer' in x)

                for card in game_cards:
                    game_data = {}

                    # Extract day number
                    day_span = card.find('span', class_=lambda x: x and 'absolute' in x and 'top-1' in x and 'left-1/2' in x)
                    if day_span:
                        game_data['day'] = day_span.get_text().strip()

                    # Extract weather icon
                    weather_span = card.find('span', class_=lambda x: x and 'absolute' in x and 'top-1' in x and 'right-1' in x)
                    if weather_span:
                        game_data['weather'] = weather_span.get_text().strip()

                    # Extract home/away indicator
                    location_span = card.find('span', class_=lambda x: x and 'absolute' in x and 'top-1' in x and 'left-1' in x)
                    if location_span:
                        location_text = location_span.get_text().strip()
                        game_data['location'] = 'home' if location_text == '🏠' else 'away'

                    # Extract opponent team
                    team_spans = card.find_all('span', class_=lambda x: x and 'text-xs' in x and 'font-semibold' in x)
                    for span in team_spans:
                        text = span.get_text().strip()
                        if len(text) > 5:  # Team names are longer
                            game_data['opponent'] = text
                            break

                    # Extract team emoji
                    emoji_span = card.find('span', class_=lambda x: x and 'text-lg' in x)
                    if emoji_span:
                        game_data['opponent_emoji'] = emoji_span.get_text().strip()

                    # Extract score
                    score_div = card.find('div', class_=lambda x: x and 'text-sm' in x and 'font-bold' in x)
                    if score_div:
                        score_text = score_div.get_text().strip()
                        if '-' in score_text:
                            game_data['score'] = score_text
                            scores = score_text.split('-')
                            if len(scores) == 2:
                                if game_data.get('location') == 'home':
                                    game_data['home_score'] = scores[0]
                                    game_data['away_score'] = scores[1]
                                else:
                                    game_data['away_score'] = scores[0]
                                    game_data['home_score'] = scores[1]

                    # Extract W/L result
                    result_span = card.find('span', class_=lambda x: x and 'absolute' in x and 'bottom-1' in x and 'left-1' in x)
                    if result_span:
                        result_text = result_span.get_text().strip()
                        game_data['result'] = result_text

                    # Extract background color for team identification
                    style = card.get('style', '')
                    if 'background-color:' in style:
                        # Extract RGB values for team color
                        import re
                        color_match = re.search(r'background-color:\s*rgb\(([^)]+)\)', style)
                        if color_match:
                            game_data['team_color'] = color_match.group(1)

                    if game_data:
                        games.append(game_data)

        except Exception as e:
            print(f"Error extracting schedule games: {e}")

        return games

    def extract_game_data(self, soup):
        """Extract detailed game data from the parsed HTML."""
        data = {
            'game_info': self.extract_game_info(soup),
            'game_events': self.extract_game_events(soup),
            'play_by_play': self.extract_play_by_play(soup)
        }
        return data

    def extract_game_info(self, soup):
        """Extract basic game information from header."""
        game_info = {}

        try:
            # Look for the game result text directly in the HTML
            # Search for text containing "defeated" which indicates game result
            all_text = soup.get_text()

            # Look for the pattern "Team A defeated Team B. Final score: X-Y"
            import re
            defeated_pattern = r'([^.]+)\s+defeated\s+([^.]+)\.\s*Final score:\s*(\d+-\d+)'
            match = re.search(defeated_pattern, all_text, re.IGNORECASE)

            if match:
                game_info['winning_team'] = match.group(1).strip()
                game_info['losing_team'] = match.group(2).strip()
                game_info['final_score'] = match.group(3).strip()
                game_info['title'] = f"{match.group(1).strip()} defeated {match.group(2).strip()}"

                # Extract individual scores
                scores = match.group(3).split('-')
                if len(scores) == 2:
                    game_info['winning_score'] = scores[0].strip()
                    game_info['losing_score'] = scores[1].strip()

            # Also try the original method as fallback
            header = soup.find('div', class_=lambda x: x and 'relative' in x and 'w-full' in x and 'h-28' in x)

            if header and not game_info:
                # Game title/teams
                title_div = header.find('div', style=lambda x: x and 'font-size:' in x)
                if title_div:
                    title_text = title_div.get_text().strip()
                    game_info['title'] = title_text

                    # Parse teams from title (e.g., "Team A defeated Team B")
                    if 'defeated' in title_text:
                        parts = title_text.split(' defeated ')
                        if len(parts) == 2:
                            game_info['winning_team'] = parts[0].strip()
                            game_info['losing_team'] = parts[1].strip()

                # Final score
                score_divs = header.find_all('div', style=lambda x: x and 'font-size:' in x)
                for div in score_divs:
                    text = div.get_text().strip()
                    if 'FINAL' in text and '-' in text:
                        game_info['final_score'] = text
                        # Extract individual scores
                        score_part = text.replace('FINAL ', '').strip()
                        if '-' in score_part:
                            scores = score_part.split('-')
                            if len(scores) == 2:
                                game_info['winning_score'] = scores[0].strip()
                                game_info['losing_score'] = scores[1].strip()

            # Look for special events
            if '📦' in all_text or 'Special Delivery' in all_text:
                delivery_pattern = r'([^.]*📦[^.]*Special Delivery[^.]*)'
                delivery_match = re.search(delivery_pattern, all_text)
                if delivery_match:
                    game_info['special_event'] = delivery_match.group(1).strip()

        except Exception as e:
            print(f"Error extracting game info: {e}")

        return game_info

    def extract_game_events(self, soup):
        """Extract game events and highlights."""
        events = []

        try:
            # Look for event containers
            event_containers = soup.find_all('div', class_=lambda x: x and 'bg-[#1c2a3a]' in x)

            for container in event_containers:
                event_text = container.get_text().strip()
                if event_text and len(event_text) > 10:  # Filter out empty or very short text
                    event_data = {
                        'text': event_text,
                        'type': self.classify_game_event(event_text)
                    }
                    events.append(event_data)

        except Exception as e:
            print(f"Error extracting game events: {e}")

        return events

    def extract_play_by_play(self, soup):
        """Extract detailed play-by-play data."""
        plays = []

        try:
            # Look for play-by-play sections
            # This would need more specific selectors based on the actual HTML structure
            play_containers = soup.find_all('div', class_=lambda x: x and 'text-sm' in x)

            for container in play_containers:
                play_text = container.get_text().strip()

                # Look for baseball action patterns
                if any(keyword in play_text.lower() for keyword in [
                    'strike', 'ball', 'hit', 'out', 'run', 'mph', 'grounds', 'flies', 'singles', 'doubles'
                ]):
                    play_data = {
                        'text': play_text,
                        'type': self.classify_play_action(play_text)
                    }

                    # Extract MPH if present
                    import re
                    mph_match = re.search(r'(\d+)\s*MPH', play_text, re.IGNORECASE)
                    if mph_match:
                        play_data['velocity'] = int(mph_match.group(1))

                    plays.append(play_data)

        except Exception as e:
            print(f"Error extracting play-by-play: {e}")

        return plays

    def classify_game_event(self, event_text):
        """Classify the type of game event."""
        text_lower = event_text.lower()

        if 'delivery' in text_lower or '📦' in event_text:
            return 'special_delivery'
        elif 'inning' in text_lower:
            return 'inning_change'
        elif 'score' in text_lower:
            return 'scoring'
        elif any(word in text_lower for word in ['win', 'lose', 'final']):
            return 'game_result'
        else:
            return 'other'

    def classify_play_action(self, play_text):
        """Classify the type of play action."""
        text_lower = play_text.lower()

        if 'strike' in text_lower:
            return 'strike'
        elif 'ball' in text_lower:
            return 'ball'
        elif any(word in text_lower for word in ['single', 'double', 'triple', 'home run']):
            return 'hit'
        elif 'out' in text_lower:
            return 'out'
        elif 'run' in text_lower and 'scores' in text_lower:
            return 'run_scored'
        else:
            return 'other'

def main():
    """Main function to test the scraper."""
    scraper = MMOLBScraper()

    # Test team page
    team_file = "mmolb html/team page.html"
    if os.path.exists(team_file):
        print("=== PARSING TEAM PAGE ===")
        team_data = scraper.parse_html_file(team_file, "team")

        if team_data:
            print(f"Team Info: {team_data['team_info']}")
            print(f"Roster: {len(team_data['roster'])} players")
            print(f"Recent Events: {len(team_data['recent_events'])} events")

            # Save team data
            scraper.save_to_csv(team_data, "scraped_data/team")

            with open('scraped_data/team_data.json', 'w', encoding='utf-8') as f:
                json.dump(team_data, f, indent=2, ensure_ascii=False)

            print("✅ Team page scraping completed!")
        else:
            print("❌ Failed to parse team page")

    # Test player page
    player_file = "mmolb html/player page.htm"
    if os.path.exists(player_file):
        print("\n=== PARSING PLAYER PAGE ===")
        player_data = scraper.parse_html_file(player_file, "player")

        if player_data:
            print(f"Player Info: {player_data['player_info']}")
            print(f"Profile: {player_data['player_profile']}")
            print(f"Equipment: {len(player_data['player_equipment'])} items")
            print(f"Stats Categories: {list(player_data['player_stats'].keys())}")
            print(f"Events: {len(player_data['player_events'])} events")

            # Save player data
            scraper.save_player_data_to_csv(player_data, "scraped_data/player")

            with open('scraped_data/player_data.json', 'w', encoding='utf-8') as f:
                json.dump(player_data, f, indent=2, ensure_ascii=False)

            print("✅ Player page scraping completed!")
        else:
            print("❌ Failed to parse player page")

    # Test schedule page
    schedule_file = "mmolb html/season schedule.htm"
    if os.path.exists(schedule_file):
        print("\n=== PARSING SCHEDULE PAGE ===")
        schedule_data = scraper.parse_html_file(schedule_file, "schedule")

        if schedule_data:
            print(f"Schedule Info: {schedule_data['schedule_info']}")
            print(f"Games: {len(schedule_data['games'])} games found")

            # Save schedule data
            os.makedirs("scraped_data/schedule", exist_ok=True)

            # Save games to CSV
            if schedule_data['games']:
                games_file = "scraped_data/schedule/schedule_games.csv"
                with open(games_file, 'w', newline='', encoding='utf-8') as f:
                    fieldnames = set()
                    for game in schedule_data['games']:
                        fieldnames.update(game.keys())

                    writer = csv.DictWriter(f, fieldnames=sorted(fieldnames))
                    writer.writeheader()
                    writer.writerows(schedule_data['games'])

            with open('scraped_data/schedule_data.json', 'w', encoding='utf-8') as f:
                json.dump(schedule_data, f, indent=2, ensure_ascii=False)

            print("✅ Schedule page scraping completed!")
        else:
            print("❌ Failed to parse schedule page")

    # Test game page
    game_file = "mmolb html/game page.htm"
    if os.path.exists(game_file):
        print("\n=== PARSING GAME PAGE ===")
        game_data = scraper.parse_html_file(game_file, "game")

        if game_data:
            print(f"Game Info: {game_data['game_info']}")
            print(f"Game Events: {len(game_data['game_events'])} events")
            print(f"Play-by-Play: {len(game_data['play_by_play'])} plays")

            # Save game data
            os.makedirs("scraped_data/game", exist_ok=True)

            # Save events and plays to CSV
            if game_data['game_events']:
                events_file = "scraped_data/game/game_events.csv"
                with open(events_file, 'w', newline='', encoding='utf-8') as f:
                    fieldnames = ['text', 'type']
                    writer = csv.DictWriter(f, fieldnames=fieldnames)
                    writer.writeheader()
                    writer.writerows(game_data['game_events'])

            if game_data['play_by_play']:
                plays_file = "scraped_data/game/play_by_play.csv"
                with open(plays_file, 'w', newline='', encoding='utf-8') as f:
                    fieldnames = set()
                    for play in game_data['play_by_play']:
                        fieldnames.update(play.keys())

                    writer = csv.DictWriter(f, fieldnames=sorted(fieldnames))
                    writer.writeheader()
                    writer.writerows(game_data['play_by_play'])

            with open('scraped_data/game_data.json', 'w', encoding='utf-8') as f:
                json.dump(game_data, f, indent=2, ensure_ascii=False)

            print("✅ Game page scraping completed!")
        else:
            print("❌ Failed to parse game page")

    print(f"\n🎉 All available pages have been processed!")
    print("Check the scraped_data/ directory for extracted data.")

if __name__ == "__main__":
    main()
