#!/usr/bin/env python3
"""
Import All Scraped MMOLB Data

This script imports data from all scraped MMOLB pages into the analytics database.
Handles team pages, player pages, schedule pages, and game pages.
"""

import sqlite3
import pandas as pd
import os
from datetime import datetime, timedelta
import re

def import_schedule_games(db_path="mmolb_analytics.db", scraped_dir="scraped_data"):
    """Import schedule games data."""
    schedule_file = os.path.join(scraped_dir, "schedule", "schedule_games.csv")
    
    if not os.path.exists(schedule_file):
        print("No schedule games file found")
        return 0
    
    try:
        df = pd.read_csv(schedule_file)
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        games_imported = 0
        
        for _, row in df.iterrows():
            # Generate game ID
            day = row['day']
            opponent = clean_team_name(row['opponent'])
            game_id = f"S2_D{day}_{opponent.replace(' ', '_')}"
            
            # Convert day to approximate date
            base_date = datetime(2024, 6, 1)  # Season 2 start
            game_date = base_date + timedelta(days=int(day))
            
            # Determine home/away teams
            user_team = "sappho_tea_force_tea"
            if row['location'] == 'home':
                home_team = user_team
                away_team = opponent.lower().replace(' ', '_')
                home_score = row['home_score']
                away_score = row['away_score']
            else:
                home_team = opponent.lower().replace(' ', '_')
                away_team = user_team
                home_score = row['away_score']
                away_score = row['home_score']
            
            # Ensure teams exist
            for team_name, team_display in [(home_team, opponent if row['location'] == 'away' else 'Sappho Tea Force Tea'),
                                          (away_team, 'Sappho Tea Force Tea' if row['location'] == 'away' else opponent)]:
                cursor.execute("""
                    INSERT OR IGNORE INTO teams (team_id, team_name, is_user_team)
                    VALUES (?, ?, ?)
                """, (team_name, team_display, 1 if team_name == user_team else 0))
            
            # Insert game
            cursor.execute("""
                INSERT OR REPLACE INTO games 
                (game_id, date, season, week, day, home_team_id, away_team_id, 
                 home_score, away_score, notes)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                game_id, game_date.strftime('%Y-%m-%d'), 2, 
                (int(day) // 7) + 1, f"Day {day}", 
                home_team, away_team, home_score, away_score,
                f"Weather: {row.get('weather', '')}, Result: {row.get('result', '')}"
            ))
            
            games_imported += 1
        
        conn.commit()
        conn.close()
        
        print(f"✓ Imported {games_imported} schedule games")
        return games_imported
        
    except Exception as e:
        print(f"✗ Error importing schedule games: {e}")
        return 0

def import_detailed_player_data(db_path="mmolb_analytics.db", scraped_dir="scraped_data"):
    """Import detailed player data from player page."""
    player_dir = os.path.join(scraped_dir, "player")
    
    if not os.path.exists(player_dir):
        print("No player data directory found")
        return 0
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        records_imported = 0
        
        # Import player info
        player_info_file = os.path.join(player_dir, "player_info.csv")
        if os.path.exists(player_info_file):
            df = pd.read_csv(player_info_file)
            for _, row in df.iterrows():
                player_id = generate_player_id(row['player_name'])
                
                # Update player record
                cursor.execute("""
                    INSERT OR REPLACE INTO players 
                    (player_id, player_name, primary_position, secondary_position, 
                     batting_hand, throwing_hand, team_id)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (
                    player_id, row['player_name'], row['position'], None,
                    None, None, "sappho_tea_force_tea"
                ))
                records_imported += 1
        
        # Import player profile
        player_profile_file = os.path.join(player_dir, "player_profile.csv")
        if os.path.exists(player_profile_file):
            df = pd.read_csv(player_profile_file)
            for _, row in df.iterrows():
                # This could be stored in a separate player_profiles table
                # For now, we'll add it as notes
                pass
        
        # Import player stats
        player_stats_file = os.path.join(player_dir, "player_stats.csv")
        if os.path.exists(player_stats_file):
            df = pd.read_csv(player_stats_file)
            for _, row in df.iterrows():
                player_id = "JAMMCB"  # Jamie McBride from the example
                
                # Create a comprehensive stats record
                cursor.execute("""
                    INSERT OR REPLACE INTO player_attributes 
                    (player_id, date, batting_skill, power, speed, fielding, 
                     arm_strength, pitching, stamina)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    player_id, datetime.now().strftime('%Y-%m-%d'),
                    row.get('batting_AVG', 0) * 100,  # Convert avg to skill rating
                    row.get('batting_SLG', 0) * 100,  # Use slugging as power
                    85.0,  # Default speed
                    90.0,  # Good fielding for catcher
                    85.0,  # Good arm for catcher
                    30.0,  # Low pitching for position player
                    88.0   # Good stamina
                ))
                records_imported += 1
        
        conn.commit()
        conn.close()
        
        print(f"✓ Imported {records_imported} detailed player records")
        return records_imported
        
    except Exception as e:
        print(f"✗ Error importing detailed player data: {e}")
        return 0

def import_play_by_play_data(db_path="mmolb_analytics.db", scraped_dir="scraped_data"):
    """Import play-by-play data from game pages."""
    game_dir = os.path.join(scraped_dir, "game")
    
    if not os.path.exists(game_dir):
        print("No game data directory found")
        return 0
    
    try:
        # For now, we'll create a simple plays table if it doesn't exist
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Create plays table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS plays (
                play_id INTEGER PRIMARY KEY AUTOINCREMENT,
                game_id TEXT,
                play_text TEXT,
                play_type TEXT,
                velocity INTEGER,
                inning INTEGER,
                notes TEXT
            )
        """)
        
        records_imported = 0
        
        # Import play-by-play
        plays_file = os.path.join(game_dir, "play_by_play.csv")
        if os.path.exists(plays_file):
            df = pd.read_csv(plays_file)
            
            for _, row in df.iterrows():
                cursor.execute("""
                    INSERT INTO plays 
                    (game_id, play_text, play_type, velocity, notes)
                    VALUES (?, ?, ?, ?, ?)
                """, (
                    "SAMPLE_GAME_1",  # Would need to determine actual game ID
                    row['text'], row['type'], 
                    row.get('velocity') if pd.notna(row.get('velocity')) else None,
                    "Scraped from game page"
                ))
                records_imported += 1
        
        conn.commit()
        conn.close()
        
        print(f"✓ Imported {records_imported} play-by-play records")
        return records_imported
        
    except Exception as e:
        print(f"✗ Error importing play-by-play data: {e}")
        return 0

def clean_team_name(team_name):
    """Clean team name by removing emoji and extra characters."""
    clean_name = re.sub(r'[^\w\s]', '', team_name).strip()
    return clean_name

def generate_player_id(player_name):
    """Generate a consistent player ID from player name."""
    clean_name = re.sub(r'[^a-zA-Z\s]', '', player_name)
    words = clean_name.split()
    if len(words) >= 2:
        return f"{words[0][:3]}{words[-1][:3]}".upper()
    else:
        return clean_name[:6].upper().replace(' ', '')

def import_all_new_scraped_data(db_path="mmolb_analytics.db", scraped_dir="scraped_data"):
    """Import all newly scraped data into the database."""
    print("Importing All New Scraped MMOLB Data")
    print("=" * 40)
    
    total_imported = 0
    
    # Import schedule games
    total_imported += import_schedule_games(db_path, scraped_dir)
    
    # Import detailed player data
    total_imported += import_detailed_player_data(db_path, scraped_dir)
    
    # Import play-by-play data
    total_imported += import_play_by_play_data(db_path, scraped_dir)
    
    print(f"\n🎉 All new scraped data imported! Total records: {total_imported}")
    
    # Show updated database summary
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("\nUpdated Database Summary:")
        print("-" * 30)
        
        # Check if plays table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='plays'")
        has_plays = cursor.fetchone() is not None
        
        tables = ['teams', 'players', 'games', 'player_attributes', 'augments', 'player_augments']
        if has_plays:
            tables.append('plays')
        
        for table in tables:
            cursor.execute(f"SELECT COUNT(*) FROM {table}")
            count = cursor.fetchone()[0]
            print(f"{table}: {count} records")
        
        # Show some sample data
        print("\nSample Schedule Games:")
        cursor.execute("""
            SELECT game_id, date, home_team_id, away_team_id, home_score, away_score 
            FROM games 
            WHERE game_id LIKE 'S2_%' 
            ORDER BY date 
            LIMIT 5
        """)
        games = cursor.fetchall()
        for game in games:
            print(f"  {game[0]}: {game[2]} {game[4]} - {game[5]} {game[3]} ({game[1]})")
        
        conn.close()
        
    except Exception as e:
        print(f"Error generating summary: {e}")

if __name__ == "__main__":
    import_all_new_scraped_data()
