#!/usr/bin/env python3
"""
Automated MMOLB Team Data Collector

This script automates the collection of data specifically for your team:
- Your team roster and player stats
- Game results and player performance
- Augment tracking and decisions
- Opponent interaction data (scores, matchups only)
"""

import os
import sqlite3
import json
from datetime import datetime
from mmolb_scraper import MMOLBScraper

class TeamDataCollector:
    def __init__(self, team_name="Sappho Tea Force Tea", db_path="mmolb_analytics.db"):
        self.team_name = team_name
        self.team_id = team_name.lower().replace(' ', '_')
        self.db_path = db_path
        self.scraper = MMOLBScraper()
        self.collection_log = []
    
    def collect_team_data(self, team_page_path):
        """Collect comprehensive team data from team page."""
        print(f"🏠 Collecting data for {self.team_name}...")
        
        if not os.path.exists(team_page_path):
            print(f"❌ Team page not found: {team_page_path}")
            return False
        
        try:
            # Parse team page
            team_data = self.scraper.parse_html_file(team_page_path, "team")
            
            if not team_data:
                print("❌ Failed to parse team page")
                return False
            
            # Store team data
            self.store_team_info(team_data['team_info'])
            self.store_roster_data(team_data['roster'])
            self.store_team_events(team_data['recent_events'])
            
            self.log_collection("team_page", len(team_data['roster']), len(team_data['recent_events']))
            print(f"✅ Team data collected: {len(team_data['roster'])} players, {len(team_data['recent_events'])} events")
            
            return True
            
        except Exception as e:
            print(f"❌ Error collecting team data: {e}")
            return False
    
    def collect_player_data(self, player_page_paths):
        """Collect detailed data for your team's players."""
        print(f"👥 Collecting player data...")
        
        total_players = 0
        
        for player_file in player_page_paths:
            if not os.path.exists(player_file):
                print(f"⚠️ Player page not found: {player_file}")
                continue
            
            try:
                player_data = self.scraper.parse_html_file(player_file, "player")
                
                if player_data:
                    self.store_player_details(player_data)
                    total_players += 1
                    print(f"✅ Collected data for {player_data['player_info'].get('player_name', 'Unknown')}")
                
            except Exception as e:
                print(f"❌ Error collecting player data from {player_file}: {e}")
        
        self.log_collection("player_pages", total_players, 0)
        print(f"✅ Player data collection complete: {total_players} players")
        
        return total_players > 0
    
    def collect_schedule_data(self, schedule_page_path):
        """Collect game schedule focusing on your team's games."""
        print(f"📅 Collecting schedule data...")
        
        if not os.path.exists(schedule_page_path):
            print(f"❌ Schedule page not found: {schedule_page_path}")
            return False
        
        try:
            schedule_data = self.scraper.parse_html_file(schedule_page_path, "schedule")
            
            if not schedule_data:
                print("❌ Failed to parse schedule page")
                return False
            
            # Filter and store games involving your team
            team_games = self.filter_team_games(schedule_data['games'])
            self.store_schedule_games(team_games)
            
            self.log_collection("schedule_page", len(team_games), 0)
            print(f"✅ Schedule data collected: {len(team_games)} games")
            
            return True
            
        except Exception as e:
            print(f"❌ Error collecting schedule data: {e}")
            return False
    
    def collect_game_data(self, game_page_paths):
        """Collect detailed game data for games involving your team."""
        print(f"⚾ Collecting game data...")
        
        total_games = 0
        
        for game_file in game_page_paths:
            if not os.path.exists(game_file):
                print(f"⚠️ Game page not found: {game_file}")
                continue
            
            try:
                game_data = self.scraper.parse_html_file(game_file, "game")
                
                if game_data:
                    # Only store if it involves your team
                    if self.is_team_game(game_data):
                        self.store_game_details(game_data, game_file)
                        total_games += 1
                        print(f"✅ Collected game data from {os.path.basename(game_file)}")
                    else:
                        print(f"⏭️ Skipping non-team game: {os.path.basename(game_file)}")
                
            except Exception as e:
                print(f"❌ Error collecting game data from {game_file}: {e}")
        
        self.log_collection("game_pages", total_games, 0)
        print(f"✅ Game data collection complete: {total_games} games")
        
        return total_games > 0
    
    def store_team_info(self, team_info):
        """Store team information in database."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("""
                INSERT OR REPLACE INTO teams (team_id, team_name, is_user_team)
                VALUES (?, ?, ?)
            """, (self.team_id, team_info.get('name', self.team_name), 1))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"Error storing team info: {e}")
    
    def store_roster_data(self, roster):
        """Store roster data focusing on your team's players."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            for player in roster:
                player_id = self.generate_player_id(player['name'])
                
                cursor.execute("""
                    INSERT OR REPLACE INTO players 
                    (player_id, player_name, primary_position, secondary_position, 
                     batting_hand, throwing_hand, team_id)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (
                    player_id, player['name'], player['position'], None,
                    None, None, self.team_id
                ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"Error storing roster data: {e}")
    
    def store_team_events(self, events):
        """Store team events, focusing on augments and player changes."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            for event in events:
                if event.get('type') in ['stat_gain', 'enchantment', 'item_delivery']:
                    # This is an augment-related event for your team
                    self.store_augment_event(cursor, event)
                elif event.get('type') == 'game':
                    # Game result involving your team
                    self.store_game_result(cursor, event)
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"Error storing team events: {e}")
    
    def store_augment_event(self, cursor, event):
        """Store augment-related events for your players."""
        if 'player' not in event:
            return
        
        player_name = event['player']
        player_id = self.generate_player_id(player_name)
        
        # Create augment record
        if event['type'] == 'stat_gain':
            augment_name = f"{event['stat_name']} Boost +{event['stat_increase']}"
            augment_type = "Stat Boost"
            stat_affected = event['stat_name'].lower()
            description = f"Increases {event['stat_name']} by {event['stat_increase']}"
        elif event['type'] == 'enchantment':
            augment_name = f"{event['item']} Enchantment +{event['stat_increase']} {event['stat_name']}"
            augment_type = "Enchantment"
            stat_affected = event['stat_name'].lower()
            description = f"Enchanted {event['item']} with +{event['stat_increase']} {event['stat_name']}"
        else:
            return
        
        # Insert augment
        cursor.execute("""
            INSERT OR IGNORE INTO augments 
            (augment_name, augment_type, stat_affected, description)
            VALUES (?, ?, ?, ?)
        """, (augment_name, augment_type, stat_affected, description))
        
        # Get augment ID
        cursor.execute("SELECT augment_id FROM augments WHERE augment_name = ?", (augment_name,))
        result = cursor.fetchone()
        if result:
            augment_id = result[0]
            
            # Convert day to date
            base_date = datetime(2024, 6, 1) if event.get('season', 1) == 2 else datetime(2024, 1, 1)
            event_date = base_date.replace(day=min(event.get('day', 1), 28))  # Avoid invalid dates
            
            # Insert player augment
            cursor.execute("""
                INSERT OR REPLACE INTO player_augments 
                (player_id, augment_id, date_applied, before_value, after_value, 
                 observed_effect, duration, is_active, notes)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                player_id, augment_id, event_date.strftime('%Y-%m-%d'),
                None, None, event.get('raw_text', ''), 
                7 if event['type'] == 'stat_gain' else 30, 1,
                f"Auto-collected from team events - Season {event.get('season', 'Unknown')}"
            ))
    
    def filter_team_games(self, games):
        """Filter games to only include those involving your team."""
        team_games = []

        for game in games:
            # Check if this game involves your team
            # Schedule pages show opponent names, so all games in schedule are your team's games
            if game.get('opponent'):  # If there's an opponent, it's your team's game
                team_games.append(game)

        return team_games
    
    def is_team_game(self, game_data):
        """Check if game data involves your team."""
        game_info = game_data.get('game_info', {})
        title = game_info.get('title', '').lower()
        winning_team = game_info.get('winning_team', '').lower()
        losing_team = game_info.get('losing_team', '').lower()
        final_score = game_info.get('final_score', '').lower()

        # Check all game events for team mentions too
        game_events = game_data.get('game_events', [])
        all_event_text = ' '.join([event.get('text', '').lower() for event in game_events])

        # Also check play-by-play text
        plays = game_data.get('play_by_play', [])
        all_play_text = ' '.join([play.get('text', '').lower() for play in plays])

        # Check if Sappho Tea Force Tea is mentioned anywhere
        team_keywords = ['sappho', 'tea force']

        search_text = f"{title} {winning_team} {losing_team} {final_score} {all_event_text} {all_play_text}"

        return any(keyword in search_text for keyword in team_keywords)
    
    def generate_player_id(self, player_name):
        """Generate consistent player ID."""
        import re
        clean_name = re.sub(r'[^a-zA-Z\s]', '', player_name)
        words = clean_name.split()
        if len(words) >= 2:
            return f"{words[0][:3]}{words[-1][:3]}".upper()
        else:
            return clean_name[:6].upper().replace(' ', '')
    
    def store_player_details(self, player_data):
        """Store detailed player information."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            player_info = player_data.get('player_info', {})
            player_name = player_info.get('player_name', 'Unknown')
            player_id = self.generate_player_id(player_name)

            # Update player record
            cursor.execute("""
                INSERT OR REPLACE INTO players
                (player_id, player_name, primary_position, secondary_position,
                 batting_hand, throwing_hand, team_id)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (
                player_id, player_name, player_info.get('position', ''), None,
                None, None, self.team_id
            ))

            # Store player attributes if we have stats
            player_stats = player_data.get('player_stats', {})
            if player_stats.get('batting'):
                batting_stats = player_stats['batting']
                cursor.execute("""
                    INSERT OR REPLACE INTO player_attributes
                    (player_id, date, batting_skill, power, speed, fielding,
                     arm_strength, pitching, stamina)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    player_id, datetime.now().strftime('%Y-%m-%d'),
                    batting_stats.get('AVG', 0) * 100 if isinstance(batting_stats.get('AVG'), (int, float)) else 50,
                    batting_stats.get('SLG', 0) * 100 if isinstance(batting_stats.get('SLG'), (int, float)) else 50,
                    85.0, 90.0, 85.0, 30.0, 88.0
                ))

            conn.commit()
            conn.close()

        except Exception as e:
            print(f"Error storing player details: {e}")

    def store_schedule_games(self, games):
        """Store schedule games in database."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            for game in games:
                day = game.get('day', 1)
                opponent = game.get('opponent', 'Unknown')

                # Generate game ID
                game_id = f"S2_D{day}_{opponent.replace(' ', '_')}"

                # Convert day to date
                base_date = datetime(2024, 6, 1)
                try:
                    game_date = base_date.replace(day=min(int(day), 28))
                except:
                    game_date = base_date

                # Determine scores and teams
                location = game.get('location', 'home')
                if location == 'home':
                    home_team = self.team_id
                    away_team = opponent.lower().replace(' ', '_')
                    home_score = game.get('home_score', 0)
                    away_score = game.get('away_score', 0)
                else:
                    home_team = opponent.lower().replace(' ', '_')
                    away_team = self.team_id
                    home_score = game.get('away_score', 0)
                    away_score = game.get('home_score', 0)

                # Ensure opponent team exists
                cursor.execute("""
                    INSERT OR IGNORE INTO teams (team_id, team_name, is_user_team)
                    VALUES (?, ?, ?)
                """, (opponent.lower().replace(' ', '_'), opponent, 0))

                # Insert game
                cursor.execute("""
                    INSERT OR REPLACE INTO games
                    (game_id, date, season, week, day, home_team_id, away_team_id,
                     home_score, away_score, notes)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    game_id, game_date.strftime('%Y-%m-%d'), 2,
                    (int(day) // 7) + 1, f"Day {day}",
                    home_team, away_team, home_score, away_score,
                    f"Weather: {game.get('weather', '')}, Result: {game.get('result', '')}"
                ))

            conn.commit()
            conn.close()

        except Exception as e:
            print(f"Error storing schedule games: {e}")

    def store_game_details(self, game_data, game_file):
        """Store detailed game information."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # Create plays table if it doesn't exist
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS plays (
                    play_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    game_id TEXT,
                    play_text TEXT,
                    play_type TEXT,
                    velocity INTEGER,
                    inning INTEGER,
                    notes TEXT
                )
            """)

            # Store play-by-play data
            plays = game_data.get('play_by_play', [])
            game_id = f"GAME_{os.path.basename(game_file).replace('.htm', '')}"

            for play in plays:
                cursor.execute("""
                    INSERT INTO plays
                    (game_id, play_text, play_type, velocity, notes)
                    VALUES (?, ?, ?, ?, ?)
                """, (
                    game_id, play.get('text', ''), play.get('type', ''),
                    play.get('velocity') if play.get('velocity') else None,
                    f"Auto-collected from {game_file}"
                ))

            conn.commit()
            conn.close()

        except Exception as e:
            print(f"Error storing game details: {e}")

    def store_game_result(self, cursor, event):
        """Store game result from team events."""
        try:
            if event.get('type') != 'game':
                return

            day = event.get('day', 1)
            season = event.get('season', 2)

            # Generate game ID
            home_team = event.get('home_team', '').replace(' ', '_').lower()
            away_team = event.get('away_team', '').replace(' ', '_').lower()
            game_id = f"S{season}_D{day}_{home_team}_vs_{away_team}"

            # Convert day to date
            base_date = datetime(2024, 6, 1) if season == 2 else datetime(2024, 1, 1)
            try:
                game_date = base_date.replace(day=min(int(day), 28))
            except:
                game_date = base_date

            # Ensure teams exist
            for team_name in [home_team, away_team]:
                if team_name and team_name != self.team_id:
                    cursor.execute("""
                        INSERT OR IGNORE INTO teams (team_id, team_name, is_user_team)
                        VALUES (?, ?, ?)
                    """, (team_name, team_name.replace('_', ' ').title(), 0))

            # Insert game
            cursor.execute("""
                INSERT OR REPLACE INTO games
                (game_id, date, season, week, day, home_team_id, away_team_id,
                 home_score, away_score, notes)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                game_id, game_date.strftime('%Y-%m-%d'), season,
                (int(day) // 7) + 1, f"Day {day}",
                home_team, away_team,
                event.get('home_score', 0), event.get('away_score', 0),
                f"Auto-collected from team events"
            ))

        except Exception as e:
            print(f"Error storing game result: {e}")

    def log_collection(self, source, records, events):
        """Log data collection activity."""
        self.collection_log.append({
            'timestamp': datetime.now().isoformat(),
            'source': source,
            'records_collected': records,
            'events_collected': events
        })
    
    def save_collection_log(self):
        """Save collection log to file."""
        log_file = f"collection_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(log_file, 'w') as f:
            json.dump(self.collection_log, f, indent=2)
        print(f"📝 Collection log saved to {log_file}")
    
    def run_full_collection(self, data_sources):
        """Run complete data collection process."""
        print("🚀 Starting Automated Team Data Collection")
        print("=" * 50)
        
        success_count = 0
        total_sources = 0
        
        # Collect team data
        if 'team_page' in data_sources:
            total_sources += 1
            if self.collect_team_data(data_sources['team_page']):
                success_count += 1
        
        # Collect player data
        if 'player_pages' in data_sources:
            total_sources += 1
            if self.collect_player_data(data_sources['player_pages']):
                success_count += 1
        
        # Collect schedule data
        if 'schedule_page' in data_sources:
            total_sources += 1
            if self.collect_schedule_data(data_sources['schedule_page']):
                success_count += 1
        
        # Collect game data
        if 'game_pages' in data_sources:
            total_sources += 1
            if self.collect_game_data(data_sources['game_pages']):
                success_count += 1
        
        # Save collection log
        self.save_collection_log()
        
        print(f"\n🎉 Collection Complete: {success_count}/{total_sources} sources successful")
        return success_count == total_sources

def main():
    """Example usage of the automated collector."""
    collector = TeamDataCollector()
    
    # Define data sources
    data_sources = {
        'team_page': 'mmolb html/team page.html',
        'player_pages': ['mmolb html/player page.htm'],  # Add more player pages as needed
        'schedule_page': 'mmolb html/season schedule.htm',
        'game_pages': ['mmolb html/game page.htm']  # Add more game pages as needed
    }
    
    # Run collection
    success = collector.run_full_collection(data_sources)
    
    if success:
        print("\n✅ All data collection completed successfully!")
    else:
        print("\n⚠️ Some data collection failed - check logs for details")

if __name__ == "__main__":
    main()
