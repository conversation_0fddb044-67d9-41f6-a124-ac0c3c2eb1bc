#!/usr/bin/env python3
"""
Import Scraped MMOLB Data

This script imports data scraped from MMOLB team pages into the analytics database.
"""

import sqlite3
import pandas as pd
import os
from datetime import datetime, timedelta
import re

def import_scraped_team_info(db_path="mmolb_analytics.db", scraped_dir="scraped_data"):
    """Import team information from scraped data."""
    team_file = os.path.join(scraped_dir, "team_info.csv")
    
    if not os.path.exists(team_file):
        print("No team info file found")
        return 0
    
    try:
        df = pd.read_csv(team_file)
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        for _, row in df.iterrows():
            # Create team ID from name
            team_id = row['name'].replace(' ', '_').lower()
            
            cursor.execute("""
                INSERT OR REPLACE INTO teams (team_id, team_name, is_user_team)
                VALUES (?, ?, ?)
            """, (team_id, row['name'], 1))  # Mark as user team
        
        conn.commit()
        conn.close()
        
        print(f"✓ Imported {len(df)} team records")
        return len(df)
        
    except Exception as e:
        print(f"✗ Error importing team info: {e}")
        return 0

def import_scraped_roster(db_path="mmolb_analytics.db", scraped_dir="scraped_data"):
    """Import roster data from scraped data."""
    roster_file = os.path.join(scraped_dir, "roster.csv")
    
    if not os.path.exists(roster_file):
        print("No roster file found")
        return 0
    
    try:
        df = pd.read_csv(roster_file)
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get team ID (assuming single team for now)
        cursor.execute("SELECT team_id FROM teams WHERE is_user_team = 1 LIMIT 1")
        team_result = cursor.fetchone()
        team_id = team_result[0] if team_result else "user_team"
        
        for _, row in df.iterrows():
            # Import player
            cursor.execute("""
                INSERT OR REPLACE INTO players 
                (player_id, player_name, primary_position, secondary_position, 
                 batting_hand, throwing_hand, team_id)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (
                row['player_id'], row['name'], row['position'], None,
                None, None, team_id  # We don't have batting/throwing hand from scrape
            ))
        
        conn.commit()
        conn.close()
        
        print(f"✓ Imported {len(df)} player records")
        return len(df)
        
    except Exception as e:
        print(f"✗ Error importing roster: {e}")
        return 0

def import_scraped_events(db_path="mmolb_analytics.db", scraped_dir="scraped_data"):
    """Import events data and convert to appropriate database records."""
    events_file = os.path.join(scraped_dir, "recent_events.csv")
    
    if not os.path.exists(events_file):
        print("No events file found")
        return 0
    
    try:
        df = pd.read_csv(events_file)
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        games_imported = 0
        augments_imported = 0
        
        for _, row in df.iterrows():
            if row['type'] == 'game':
                games_imported += import_game_event(cursor, row)
            elif row['type'] == 'stat_gain':
                augments_imported += import_stat_gain_event(cursor, row)
            elif row['type'] == 'enchantment':
                augments_imported += import_enchantment_event(cursor, row)
        
        conn.commit()
        conn.close()
        
        print(f"✓ Imported {games_imported} games and {augments_imported} augment events")
        return games_imported + augments_imported
        
    except Exception as e:
        print(f"✗ Error importing events: {e}")
        return 0

def import_game_event(cursor, row):
    """Import a single game event."""
    try:
        # Generate game ID
        game_id = f"S{row['season']}_D{row['day']}_{clean_team_name(row['home_team'])}_vs_{clean_team_name(row['away_team'])}"
        
        # Convert day to approximate date (Season 2, Day 50 = roughly 50 days into season)
        base_date = datetime(2024, 1, 1)  # Arbitrary start date
        if row['season'] == 2:
            base_date = datetime(2024, 6, 1)  # Season 2 starts later
        
        game_date = base_date + timedelta(days=int(row['day']))
        
        # Clean team names
        home_team = clean_team_name(row['home_team'])
        away_team = clean_team_name(row['away_team'])
        
        # Ensure teams exist
        for team_name in [home_team, away_team]:
            cursor.execute("""
                INSERT OR IGNORE INTO teams (team_id, team_name, is_user_team)
                VALUES (?, ?, ?)
            """, (team_name.lower().replace(' ', '_'), team_name, 0))
        
        # Insert game
        cursor.execute("""
            INSERT OR REPLACE INTO games 
            (game_id, date, season, week, day, home_team_id, away_team_id, 
             home_score, away_score, notes)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            game_id, game_date.strftime('%Y-%m-%d'), row['season'], 
            (int(row['day']) // 7) + 1, f"Day {row['day']}", 
            home_team.lower().replace(' ', '_'), away_team.lower().replace(' ', '_'),
            row['home_score'], row['away_score'], f"Scraped from Season {row['season']}"
        ))
        
        return 1
        
    except Exception as e:
        print(f"Error importing game event: {e}")
        return 0

def import_stat_gain_event(cursor, row):
    """Import a stat gain event as an augment."""
    try:
        # Find player ID
        player_name = row['player']
        cursor.execute("SELECT player_id FROM players WHERE player_name = ?", (player_name,))
        player_result = cursor.fetchone()
        
        if not player_result:
            print(f"Player not found: {player_name}")
            return 0
        
        player_id = player_result[0]
        
        # Create or find augment
        augment_name = f"{row['stat_name']} Boost +{row['stat_increase']}"
        cursor.execute("""
            INSERT OR IGNORE INTO augments 
            (augment_name, augment_type, stat_affected, description)
            VALUES (?, ?, ?, ?)
        """, (
            augment_name, "Stat Boost", row['stat_name'].lower(),
            f"Increases {row['stat_name']} by {row['stat_increase']}"
        ))
        
        # Get augment ID
        cursor.execute("SELECT augment_id FROM augments WHERE augment_name = ?", (augment_name,))
        augment_id = cursor.fetchone()[0]
        
        # Convert day to date
        base_date = datetime(2024, 1, 1)
        if row['season'] == 2:
            base_date = datetime(2024, 6, 1)
        event_date = base_date + timedelta(days=int(row['day']))
        
        # Insert player augment
        cursor.execute("""
            INSERT OR REPLACE INTO player_augments 
            (player_id, augment_id, date_applied, before_value, after_value, 
             observed_effect, duration, is_active, notes)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            player_id, augment_id, event_date.strftime('%Y-%m-%d'),
            None, None, f"+{row['stat_increase']} {row['stat_name']}", 
            7, 1, f"Scraped from Season {row['season']}, Day {row['day']}"
        ))
        
        return 1
        
    except Exception as e:
        print(f"Error importing stat gain event: {e}")
        return 0

def import_enchantment_event(cursor, row):
    """Import an enchantment event as an augment."""
    try:
        # Find player ID
        player_name = row['player']
        cursor.execute("SELECT player_id FROM players WHERE player_name = ?", (player_name,))
        player_result = cursor.fetchone()
        
        if not player_result:
            print(f"Player not found: {player_name}")
            return 0
        
        player_id = player_result[0]
        
        # Create augment for enchantment
        augment_name = f"{row['item']} Enchantment +{row['stat_increase']} {row['stat_name']}"
        cursor.execute("""
            INSERT OR IGNORE INTO augments 
            (augment_name, augment_type, stat_affected, description)
            VALUES (?, ?, ?, ?)
        """, (
            augment_name, "Enchantment", row['stat_name'].lower(),
            f"Enchanted {row['item']} with +{row['stat_increase']} {row['stat_name']}"
        ))
        
        # Get augment ID
        cursor.execute("SELECT augment_id FROM augments WHERE augment_name = ?", (augment_name,))
        augment_id = cursor.fetchone()[0]
        
        # Convert day to date
        base_date = datetime(2024, 1, 1)
        if row['season'] == 2:
            base_date = datetime(2024, 6, 1)
        event_date = base_date + timedelta(days=int(row['day']))
        
        # Insert player augment
        cursor.execute("""
            INSERT OR REPLACE INTO player_augments 
            (player_id, augment_id, date_applied, before_value, after_value, 
             observed_effect, duration, is_active, notes)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            player_id, augment_id, event_date.strftime('%Y-%m-%d'),
            None, None, f"Enchanted {row['item']} with +{row['stat_increase']} {row['stat_name']}", 
            30, 1, f"Scraped from Season {row['season']}, Day {row['day']}"
        ))
        
        return 1
        
    except Exception as e:
        print(f"Error importing enchantment event: {e}")
        return 0

def clean_team_name(team_name):
    """Clean team name by removing emoji and extra characters."""
    # Remove emoji and clean up
    clean_name = re.sub(r'[^\w\s]', '', team_name).strip()
    return clean_name

def import_all_scraped_data(db_path="mmolb_analytics.db", scraped_dir="scraped_data"):
    """Import all scraped data into the database."""
    print("Importing Scraped MMOLB Data")
    print("=" * 35)
    
    total_imported = 0
    
    # Import team info
    total_imported += import_scraped_team_info(db_path, scraped_dir)
    
    # Import roster
    total_imported += import_scraped_roster(db_path, scraped_dir)
    
    # Import events
    total_imported += import_scraped_events(db_path, scraped_dir)
    
    print(f"\n🎉 Scraped data import complete! Total records: {total_imported}")
    
    # Show updated database summary
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("\nUpdated Database Summary:")
        print("-" * 30)
        
        tables = ['teams', 'players', 'games', 'augments', 'player_augments']
        for table in tables:
            cursor.execute(f"SELECT COUNT(*) FROM {table}")
            count = cursor.fetchone()[0]
            print(f"{table}: {count} records")
        
        conn.close()
        
    except Exception as e:
        print(f"Error generating summary: {e}")

if __name__ == "__main__":
    import_all_scraped_data()
