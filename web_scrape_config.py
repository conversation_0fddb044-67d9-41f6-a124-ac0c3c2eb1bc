#!/usr/bin/env python3
"""
MMOLB Web Scraping Configuration

Easy setup for automated web scraping of your MMOLB team data.
"""

from web_scraper import MMOLBWebScraper
import json
import os
from datetime import datetime

class WebScrapeConfig:
    def __init__(self):
        self.config_file = "web_scrape_config.json"
        self.load_config()
    
    def load_config(self):
        """Load configuration from file."""
        default_config = {
            "team_name": "Sappho Tea Force Tea",
            "team_url": "",
            "scraping_settings": {
                "include_players": True,
                "include_schedule": True,
                "include_games": True,
                "max_players": 5,
                "max_games": 10,
                "request_delay": 1.0
            },
            "last_scrape": None,
            "auto_scrape_interval_hours": 24
        }
        
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r') as f:
                    self.config = json.load(f)
                # Merge with defaults for any missing keys
                for key, value in default_config.items():
                    if key not in self.config:
                        self.config[key] = value
            except Exception as e:
                print(f"Error loading config: {e}")
                self.config = default_config
        else:
            self.config = default_config
    
    def save_config(self):
        """Save configuration to file."""
        try:
            with open(self.config_file, 'w') as f:
                json.dump(self.config, f, indent=2)
        except Exception as e:
            print(f"Error saving config: {e}")
    
    def setup_team_url(self):
        """Interactive setup of team URL."""
        print("🔧 MMOLB Web Scraping Setup")
        print("=" * 30)
        
        current_url = self.config.get("team_url", "")
        if current_url:
            print(f"Current team URL: {current_url}")
            use_current = input("Use current URL? (y/n): ").strip().lower()
            if use_current == 'y':
                return current_url
        
        print("\n📝 Enter your team's MMOLB URL")
        print("Example: https://mmolb.com/team/your-team-name")
        print("(You can find this by going to your team page and copying the URL)")
        
        while True:
            team_url = input("\nTeam URL: ").strip()
            
            if not team_url:
                print("❌ Please enter a URL")
                continue
            
            if not team_url.startswith(('http://', 'https://')):
                print("❌ Please provide a complete URL starting with http:// or https://")
                continue
            
            # Test the URL
            print("🧪 Testing URL...")
            scraper = MMOLBWebScraper()
            soup = scraper.fetch_page(team_url)
            
            if soup:
                print("✅ URL is accessible!")
                self.config["team_url"] = team_url
                self.save_config()
                return team_url
            else:
                print("❌ Could not access URL. Please check and try again.")
                retry = input("Try again? (y/n): ").strip().lower()
                if retry != 'y':
                    return None
    
    def configure_scraping_settings(self):
        """Configure scraping preferences."""
        print("\n⚙️ Scraping Settings")
        print("-" * 20)
        
        settings = self.config["scraping_settings"]
        
        # Include players
        current = "Yes" if settings["include_players"] else "No"
        include_players = input(f"Scrape player pages? ({current}): ").strip().lower()
        if include_players in ['y', 'yes', 'true']:
            settings["include_players"] = True
        elif include_players in ['n', 'no', 'false']:
            settings["include_players"] = False
        
        # Max players
        if settings["include_players"]:
            max_players = input(f"Max players to scrape ({settings['max_players']}): ").strip()
            if max_players.isdigit():
                settings["max_players"] = int(max_players)
        
        # Include schedule
        current = "Yes" if settings["include_schedule"] else "No"
        include_schedule = input(f"Scrape schedule page? ({current}): ").strip().lower()
        if include_schedule in ['y', 'yes', 'true']:
            settings["include_schedule"] = True
        elif include_schedule in ['n', 'no', 'false']:
            settings["include_schedule"] = False
        
        # Include games
        current = "Yes" if settings["include_games"] else "No"
        include_games = input(f"Scrape game pages? ({current}): ").strip().lower()
        if include_games in ['y', 'yes', 'true']:
            settings["include_games"] = True
        elif include_games in ['n', 'no', 'false']:
            settings["include_games"] = False
        
        # Max games
        if settings["include_games"]:
            max_games = input(f"Max games to scrape ({settings['max_games']}): ").strip()
            if max_games.isdigit():
                settings["max_games"] = int(max_games)
        
        # Request delay
        delay = input(f"Delay between requests in seconds ({settings['request_delay']}): ").strip()
        try:
            settings["request_delay"] = float(delay)
        except ValueError:
            pass
        
        self.save_config()
        print("✅ Settings saved!")
    
    def run_scraping(self):
        """Run the web scraping with current configuration."""
        team_url = self.config.get("team_url")
        if not team_url:
            print("❌ No team URL configured. Run setup first.")
            return False
        
        print("🚀 Starting Web Scraping")
        print("=" * 25)
        print(f"Team: {self.config['team_name']}")
        print(f"URL: {team_url}")
        
        settings = self.config["scraping_settings"]
        
        # Create scraper
        scraper = MMOLBWebScraper(self.config["team_name"])
        scraper.request_delay = settings["request_delay"]
        
        # Run scraping
        success = scraper.scrape_team_complete(
            team_url,
            include_players=settings["include_players"],
            include_schedule=settings["include_schedule"],
            include_games=settings["include_games"],
            max_players=settings["max_players"],
            max_games=settings["max_games"]
        )
        
        if success:
            # Update last scrape time
            self.config["last_scrape"] = datetime.now().isoformat()
            self.save_config()
            
            print("\n🎉 Web scraping completed successfully!")
            return True
        else:
            print("\n❌ Web scraping failed")
            return False
    
    def show_status(self):
        """Show current configuration status."""
        print("📊 Current Configuration")
        print("=" * 25)
        print(f"Team: {self.config['team_name']}")
        print(f"URL: {self.config.get('team_url', 'Not configured')}")
        
        last_scrape = self.config.get('last_scrape')
        if last_scrape:
            print(f"Last scrape: {last_scrape}")
        else:
            print("Last scrape: Never")
        
        settings = self.config["scraping_settings"]
        print(f"\nScraping Settings:")
        print(f"  • Players: {'Yes' if settings['include_players'] else 'No'} (max {settings['max_players']})")
        print(f"  • Schedule: {'Yes' if settings['include_schedule'] else 'No'}")
        print(f"  • Games: {'Yes' if settings['include_games'] else 'No'} (max {settings['max_games']})")
        print(f"  • Request delay: {settings['request_delay']}s")

def main():
    """Main configuration interface."""
    config = WebScrapeConfig()
    
    while True:
        print("\n🌐 MMOLB Web Scraper")
        print("=" * 20)
        print("1. Show current status")
        print("2. Setup team URL")
        print("3. Configure scraping settings")
        print("4. Run scraping now")
        print("5. Exit")
        
        choice = input("\nSelect option (1-5): ").strip()
        
        if choice == '1':
            config.show_status()
        elif choice == '2':
            config.setup_team_url()
        elif choice == '3':
            config.configure_scraping_settings()
        elif choice == '4':
            config.run_scraping()
        elif choice == '5':
            print("👋 Goodbye!")
            break
        else:
            print("❌ Invalid option. Please choose 1-5.")

if __name__ == "__main__":
    main()
