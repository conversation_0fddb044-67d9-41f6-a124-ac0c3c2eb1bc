#!/usr/bin/env python3
"""
Debug script to check game detection
"""

from mmolb_scraper import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from automated_team_collector import TeamDataCollector

def debug_game_detection():
    scraper = MMOLBScraper()
    collector = TeamDataCollector()
    
    print("=== DEBUGGING GAME DETECTION ===")
    
    # Parse game page
    game_data = scraper.parse_html_file('mmolb html/game page.htm', 'game')
    
    if not game_data:
        print("❌ Failed to parse game page")
        return
    
    print("✅ Game page parsed successfully")
    print(f"Game Info: {game_data['game_info']}")
    print(f"Game Events: {len(game_data['game_events'])} events")
    print(f"Play-by-Play: {len(game_data['play_by_play'])} plays")
    
    # Check if it's detected as a team game
    is_team_game = collector.is_team_game(game_data)
    print(f"\nIs Team Game: {is_team_game}")
    
    # Show some sample events
    if game_data['game_events']:
        print("\nSample Game Events:")
        for i, event in enumerate(game_data['game_events'][:3]):
            print(f"  {i+1}. {event.get('text', '')[:100]}...")
    
    # Check what text we're searching
    game_info = game_data.get('game_info', {})
    title = game_info.get('title', '').lower()
    winning_team = game_info.get('winning_team', '').lower()
    losing_team = game_info.get('losing_team', '').lower()
    final_score = game_info.get('final_score', '').lower()

    game_events = game_data.get('game_events', [])
    all_event_text = ' '.join([event.get('text', '').lower() for event in game_events])

    # Also check play-by-play text
    plays = game_data.get('play_by_play', [])
    all_play_text = ' '.join([play.get('text', '').lower() for play in plays])

    search_text = f"{title} {winning_team} {losing_team} {final_score} {all_event_text} {all_play_text}"

    print(f"\nSearch Text (first 200 chars): {search_text[:200]}...")

    # Show some sample plays
    if plays:
        print("\nSample Plays:")
        for i, play in enumerate(plays[:10]):
            text = play.get('text', '')
            if 'sappho' in text.lower() or 'tea' in text.lower():
                print(f"  {i+1}. *** {text[:150]}...")
            else:
                print(f"  {i+1}. {text[:100]}...")

    # Look for any mention of team names in all play text
    print(f"\nLooking for team mentions in {len(plays)} plays...")
    team_mentions = []
    for i, play in enumerate(plays):
        text = play.get('text', '').lower()
        if 'sappho' in text or 'tea force' in text:
            team_mentions.append(f"Play {i+1}: {text[:100]}...")

    if team_mentions:
        print("Found team mentions:")
        for mention in team_mentions[:5]:
            print(f"  {mention}")
    else:
        print("No direct team mentions found in plays")

    team_keywords = ['sappho', 'tea force']
    for keyword in team_keywords:
        found = keyword in search_text
        print(f"Keyword '{keyword}' found: {found}")

if __name__ == "__main__":
    debug_game_detection()
