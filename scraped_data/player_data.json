{"player_info": {"team_name": "Sappho Tea Force Tea", "player_name": "<PERSON>", "jersey_number": "54", "position": "C", "team_emoji": "📏", "health_percentage": 95}, "player_profile": {"born": "Season 0, Holiday", "home": "<PERSON>ppho, WA 98363, USA", "likes": "tae kwon do", "dislikes": "blacksmithing", "bats": "S", "throws": "R"}, "player_equipment": [{"emoji": "🧢", "name": "Cap"}, {"emoji": "👕", "name": "T-Shirt"}, {"emoji": "🧤", "name": "Gloves"}, {"emoji": "👟", "name": "Sneakers"}, {"emoji": "💍", "name": "Ring"}], "player_stats": {"batting": {"AVG": 0.26, "OBP": 0.345, "SLG": 0.339, "OPS": 0.683, "H": 33, "1B": 24, "2B": 8, "3B": 1, "HR": 0, "BB": 16, "PA": 145, "AB": 127, "SB": 4, "CS": 3, "GIDP": 3}, "pitching": {"ERA": 0.0, "WHIP": 0.0, "K/BB": 0.0, "K/9": 0.0, "H/9": 0.0, "BB/9": 0.0, "HR/9": 0.0, "IP": 0.0, "K": 0, "BBP": 0, "HA": 0, "HB": 0, "ER": 0, "W": 0, "L": 0, "QS": 0, "SV": 0, "BS": 0, "G": 0, "GF": 0, "CG": 0, "SHO": 0, "NH": 0}, "defense": {"E": 0, "A": 29, "PO": 331, "DP": 1}}, "player_events": [{"raw_text": "🧩 Season 2, Regular Season, Day 46: <PERSON> gained +10 Speed.", "type": "stat_gain", "season": 2, "day": 46, "player": "<PERSON>", "stat_increase": 10, "stat_name": "Speed"}, {"raw_text": "🧩 Season 1, Postseason Round 3, Day 251: <PERSON> gained +30 Dexterity.", "type": "other", "season": 1, "day": 251}, {"raw_text": "🧩 Season 1, Postseason Round 1, Day 242: <PERSON> gained +15 Contact.", "type": "other", "season": 1, "day": 242}, {"raw_text": "📦 Season 1, Regular Season, Day 228: <PERSON> received a 💍 Ring Delivery.", "type": "item_delivery", "season": 1, "day": 228, "player": "<PERSON>", "item": "💍 Ring"}, {"raw_text": "📦 Season 1, Regular Season, Day 194: <PERSON> received a 🧢 Cap Delivery.", "type": "item_delivery", "season": 1, "day": 194, "player": "<PERSON>", "item": "🧢 Cap"}, {"raw_text": "📦 Season 1, Regular Season, Day 80: <PERSON> received a 👕 T-Shirt Delivery.", "type": "item_delivery", "season": 1, "day": 80, "player": "<PERSON>", "item": "👕 T-Shirt"}, {"raw_text": "📦 Season 1, Regular Season, Day 56: <PERSON> received a 👟 Sneakers Delivery.", "type": "item_delivery", "season": 1, "day": 56, "player": "<PERSON>", "item": "👟 Sneakers"}, {"raw_text": "📦 Season 1, Regular Season, Day 24: <PERSON> received a 🧤 Gloves Delivery.", "type": "item_delivery", "season": 1, "day": 24, "player": "<PERSON>", "item": "🧤 Gloves"}]}