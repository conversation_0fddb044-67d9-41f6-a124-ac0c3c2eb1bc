<body class="__variable_5cfdac __variable_9a8899 min-h-screen"><nav class="fixed top-0 left-0 right-0 z-50 bg-[#0c111b] text-white font-sans"><div class="relative overflow-visible"><div class="absolute inset-0 flex justify-center items-center pointer-events-none z-0"><div class="relative w-24 h-24"><img src="/baseball.svg" alt="MMOLB Logo" class="w-full h-full object-contain opacity-20"><div class="absolute inset-0 flex items-center justify-center"><svg viewBox="0 0 120 30" class="w-full h-full opacity-66"><text x="50%" y="50%" text-anchor="middle" dominant-baseline="middle" font-family="sans-serif" font-weight="bold" font-size="26" fill="#ffffff" stroke="#1c2a3a" stroke-width="8" paint-order="stroke">MMOLB</text></svg></div></div></div><div class="flex justify-between items-center px-4 py-3 sm:hidden"><button class="text-white focus:outline-none"><svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path></svg></button></div><div class="hidden sm:flex sm:justify-center sm:gap-42 py-5 z-10"><a class="text-lg font-bold tracking-wide" href="/">Home</a><div class="relative"><button class="text-lg font-bold tracking-wide cursor-pointer">Leagues</button><div class="absolute top-12 left-1/2 -translate-x-1/2 w-44 bg-[#202c3d] border border-[#2e3c50] rounded-xl p-2 shadow-xl transition-all duration-200 ease-out transform z-50
        opacity-0 -translate-y-2 scale-95 pointer-events-none"><button class="block w-full text-left px-3 py-2 rounded hover:bg-[#2a3a4a] transition cursor-pointer">Greater League</button><button class="block w-full text-left px-3 py-2 rounded hover:bg-[#2a3a4a] transition cursor-pointer">Lesser League</button></div></div><div class="relative"><button class="text-lg font-bold tracking-wide cursor-pointer">Info</button><div class="absolute top-12 left-1/2 -translate-x-1/2 w-44 bg-[#202c3d] border border-[#2e3c50] rounded-xl p-2 shadow-xl transition-all duration-200 ease-out transform z-50
        opacity-0 -translate-y-2 scale-95 pointer-events-none"><button class="block w-full text-left px-3 py-2 rounded hover:bg-[#2a3a4a] transition cursor-pointer">What is MMOLB?</button><button class="block w-full text-left px-3 py-2 rounded hover:bg-[#2a3a4a] transition cursor-pointer">Election History</button><a href="https://www.patreon.com/MMOLB" target="_blank" rel="noopener noreferrer" class="block w-full text-left px-3 py-2 rounded hover:bg-[#2a3a4a] transition cursor-pointer">Support us on Patreon</a><a href="https://discord.gg/cr3tRG2xqq" target="_blank" rel="noopener noreferrer" class="block w-full text-left px-3 py-2 rounded hover:bg-[#2a3a4a] transition cursor-pointer">Official Discord</a><a href="https://reddit.com/r/MMOLB" target="_blank" rel="noopener noreferrer" class="block w-full text-left px-3 py-2 rounded hover:bg-[#2a3a4a] transition cursor-pointer">Official Reddit</a></div></div><div class="relative"><button class="text-lg font-bold tracking-wide cursor-pointer">Account</button><div class="absolute top-12 left-1/2 -translate-x-1/2 w-52 bg-[#202c3d] border border-[#2e3c50] rounded-xl p-2 shadow-xl transition-all duration-200 ease-out transform z-50
        opacity-0 -translate-y-2 scale-95 pointer-events-none"><button class="block w-full text-left px-3 py-2 rounded hover:bg-[#2a3a4a] transition cursor-pointer">Sign In</button><button class="block w-full text-left px-3 py-2 rounded hover:bg-[#2a3a4a] transition cursor-pointer">Privacy Policy</button><button class="block w-full text-left px-3 py-2 rounded hover:bg-[#2a3a4a] transition cursor-pointer">Terms of Service</button></div></div></div></div></nav><main class="mt-16"><!--$--><!--/$--><!--$--><!--/$--><div class="min-h-screen bg-[#0c111b] text-white font-sans p-4 pt-24 max-w-2xl mx-auto"><div class="relative w-full sm:h-28 h-auto px-4 sm:px-6 py-4 border-2 rounded-2xl shadow-xl overflow-hidden mb-4 flex sm:flex-row flex-col items-center" style="background-color: rgb(157, 68, 118); color: white; border-color: white;"><div class="text-5xl sm:text-6xl flex-shrink-0 z-10">📏</div><div class="sm:absolute sm:inset-0 w-full flex flex-col items-center justify-start mt-2 sm:mt-3 pointer-events-none px-2"><div class="text-xl font-bold underline cursor-pointer pointer-events-auto hover:opacity-80 transition text-center whitespace-nowrap overflow-hidden text-ellipsis max-w-full" style="font-size: 16.5px;">Sappho Tea Force Tea</div><div class="font-extrabold text-center whitespace-nowrap overflow-hidden text-ellipsis max-w-full" style="font-size: 32.8px;">Jamie McBride</div></div><div class="text-right sm:ml-auto z-10 text-base sm:text-lg mt-2 sm:mt-0"><div class="font-semibold">#54</div><div class="font-bold">C</div></div></div><div class="w-3/4 mx-auto h-3 rounded-full bg-gray-700 mb-2"><div class="h-3 rounded-full" style="width: 95%; background-color: rgb(20, 204, 0);"></div></div><div class="flex justify-center flex-wrap gap-4 my-4"><div class="relative group"><div class="w-18 h-18 border-3 border-[#1c2a3a] text-white rounded-lg flex flex-col items-center justify-center shadow cursor-pointer"><div class="text-3xl">🧢</div><div class="text-xs font-semibold text-center mt-1 px-1" style="font-size: 11.1px;">Cap</div></div><div class="absolute bottom-[-2rem] left-1/2 -translate-x-1/2 px-3 py-1 bg-black text-white text-xs rounded w-56 text-center whitespace-pre-line z-50 pointer-events-none transition-opacity duration-200 group-hover:opacity-100 opacity-0">Cap</div></div><div class="relative group"><div class="w-18 h-18 border-3 border-[#1c2a3a] text-white rounded-lg flex flex-col items-center justify-center shadow cursor-pointer"><div class="text-3xl">👕</div><div class="text-xs font-semibold text-center mt-1 px-1" style="font-size: 9.9px;">T-Shirt</div></div><div class="absolute bottom-[-2rem] left-1/2 -translate-x-1/2 px-3 py-1 bg-black text-white text-xs rounded w-56 text-center whitespace-pre-line z-50 pointer-events-none transition-opacity duration-200 group-hover:opacity-100 opacity-0">T-Shirt</div></div><div class="relative group"><div class="w-18 h-18 border-3 border-[#1c2a3a] text-white rounded-lg flex flex-col items-center justify-center shadow cursor-pointer"><div class="text-3xl">🧤</div><div class="text-xs font-semibold text-center mt-1 px-1" style="font-size: 10.2px;">Gloves</div></div><div class="absolute bottom-[-2rem] left-1/2 -translate-x-1/2 px-3 py-1 bg-black text-white text-xs rounded w-56 text-center whitespace-pre-line z-50 pointer-events-none transition-opacity duration-200 group-hover:opacity-100 opacity-0">Gloves</div></div><div class="relative group"><div class="w-18 h-18 border-3 border-[#1c2a3a] text-white rounded-lg flex flex-col items-center justify-center shadow cursor-pointer"><div class="text-3xl">👟</div><div class="text-xs font-semibold text-center mt-1 px-1" style="font-size: 9.6px;">Sneakers</div></div><div class="absolute bottom-[-2rem] left-1/2 -translate-x-1/2 px-3 py-1 bg-black text-white text-xs rounded w-56 text-center whitespace-pre-line z-50 pointer-events-none transition-opacity duration-200 group-hover:opacity-100 opacity-0">Sneakers</div></div><div class="relative group"><div class="w-18 h-18 border-3 border-[#1c2a3a] text-white rounded-lg flex flex-col items-center justify-center shadow cursor-pointer"><div class="text-3xl">💍</div><div class="text-xs font-semibold text-center mt-1 px-1" style="font-size: 10.8px;">Ring</div></div><div class="absolute bottom-[-2rem] left-1/2 -translate-x-1/2 px-3 py-1 bg-black text-white text-xs rounded w-56 text-center whitespace-pre-line z-50 pointer-events-none transition-opacity duration-200 group-hover:opacity-100 opacity-0">Ring</div></div></div><div class="grid grid-rows-2 grid-flow-col gap-3 max-w-xl mx-auto mt-4"><div class="bg-[#1c2a3a] border border-[#2a3a4a] rounded-md px-4 py-1 flex flex-col items-center justify-center text-center"><div class="text-sm text-gray-400 font-semibold">Born</div><div class="text-base font-bold">Season 0, Holiday</div></div><div class="bg-[#1c2a3a] border border-[#2a3a4a] rounded-md px-4 py-1 flex flex-col items-center justify-center text-center"><div class="text-sm text-gray-400 font-semibold">Home</div><div class="text-base font-bold">Sappho, WA 98363, USA</div></div><div class="bg-[#1c2a3a] border border-[#2a3a4a] rounded-md px-4 py-1 flex flex-col items-center justify-center text-center"><div class="text-sm text-gray-400 font-semibold">Likes</div><div class="text-base font-bold">tae kwon do</div></div><div class="bg-[#1c2a3a] border border-[#2a3a4a] rounded-md px-4 py-1 flex flex-col items-center justify-center text-center"><div class="text-sm text-gray-400 font-semibold">Dislikes</div><div class="text-base font-bold">blacksmithing</div></div><div class="bg-[#1c2a3a] border border-[#2a3a4a] rounded-md px-4 py-1 flex flex-col items-center justify-center text-center"><div class="text-sm text-gray-400 font-semibold">Bats</div><div class="text-base font-bold">S</div></div><div class="bg-[#1c2a3a] border border-[#2a3a4a] rounded-md px-4 py-1 flex flex-col items-center justify-center text-center"><div class="text-sm text-gray-400 font-semibold">Throws</div><div class="text-base font-bold">R</div></div></div><div class="bg-[#1c2a3a] p-6 rounded-xl mt-6"><div class="text-xl font-bold mb-4 text-center">Season Stats</div><div class="mb-6"><div class="text-lg font-semibold mb-2 text-center">Batting</div><div class="grid grid-cols-3 gap-3"><div class="relative group bg-[#161d29] border border-[#2a3a4a] rounded-md p-2 flex flex-col items-center"><div class="text-sm font-bold cursor-pointer text-white">AVG</div><div class="text-base font-normal">0.260</div><div class="absolute bottom-full mb-2 px-2 py-1 bg-black text-white text-xs rounded opacity-0 group-hover:opacity-100 transition text-center whitespace-pre z-50">Batting Average</div></div><div class="relative group bg-[#161d29] border border-[#2a3a4a] rounded-md p-2 flex flex-col items-center"><div class="text-sm font-bold cursor-pointer text-white">OBP</div><div class="text-base font-normal">0.345</div><div class="absolute bottom-full mb-2 px-2 py-1 bg-black text-white text-xs rounded opacity-0 group-hover:opacity-100 transition text-center whitespace-pre z-50">On-Base Percentage</div></div><div class="relative group bg-[#161d29] border border-[#2a3a4a] rounded-md p-2 flex flex-col items-center"><div class="text-sm font-bold cursor-pointer text-white">SLG</div><div class="text-base font-normal">0.339</div><div class="absolute bottom-full mb-2 px-2 py-1 bg-black text-white text-xs rounded opacity-0 group-hover:opacity-100 transition text-center whitespace-pre z-50">Slugging Percentage</div></div><div class="relative group bg-[#161d29] border border-[#2a3a4a] rounded-md p-2 flex flex-col items-center"><div class="text-sm font-bold cursor-pointer text-white">OPS</div><div class="text-base font-normal">0.683</div><div class="absolute bottom-full mb-2 px-2 py-1 bg-black text-white text-xs rounded opacity-0 group-hover:opacity-100 transition text-center whitespace-pre z-50">On-Base Plus Slugging</div></div><div class="relative group bg-[#161d29] border border-[#2a3a4a] rounded-md p-2 flex flex-col items-center"><div class="text-sm font-bold cursor-pointer text-white">H</div><div class="text-base font-normal">33</div><div class="absolute bottom-full mb-2 px-2 py-1 bg-black text-white text-xs rounded opacity-0 group-hover:opacity-100 transition text-center whitespace-pre z-50">Hits</div></div><div class="relative group bg-[#161d29] border border-[#2a3a4a] rounded-md p-2 flex flex-col items-center"><div class="text-sm font-bold cursor-pointer text-white">1B</div><div class="text-base font-normal">24</div><div class="absolute bottom-full mb-2 px-2 py-1 bg-black text-white text-xs rounded opacity-0 group-hover:opacity-100 transition text-center whitespace-pre z-50">Singles</div></div><div class="relative group bg-[#161d29] border border-[#2a3a4a] rounded-md p-2 flex flex-col items-center"><div class="text-sm font-bold cursor-pointer text-white">2B</div><div class="text-base font-normal">8</div><div class="absolute bottom-full mb-2 px-2 py-1 bg-black text-white text-xs rounded opacity-0 group-hover:opacity-100 transition text-center whitespace-pre z-50">Doubles</div></div><div class="relative group bg-[#161d29] border border-[#2a3a4a] rounded-md p-2 flex flex-col items-center"><div class="text-sm font-bold cursor-pointer text-white">3B</div><div class="text-base font-normal">1</div><div class="absolute bottom-full mb-2 px-2 py-1 bg-black text-white text-xs rounded opacity-0 group-hover:opacity-100 transition text-center whitespace-pre z-50">Triples</div></div><div class="relative group bg-[#161d29] border border-[#2a3a4a] rounded-md p-2 flex flex-col items-center"><div class="text-sm font-bold cursor-pointer text-white">HR</div><div class="text-base font-normal">0</div><div class="absolute bottom-full mb-2 px-2 py-1 bg-black text-white text-xs rounded opacity-0 group-hover:opacity-100 transition text-center whitespace-pre z-50">Home Runs</div></div><div class="relative group bg-[#161d29] border border-[#2a3a4a] rounded-md p-2 flex flex-col items-center"><div class="text-sm font-bold cursor-pointer text-white">BB</div><div class="text-base font-normal">16</div><div class="absolute bottom-full mb-2 px-2 py-1 bg-black text-white text-xs rounded opacity-0 group-hover:opacity-100 transition text-center whitespace-pre z-50">Walks Drawn</div></div><div class="relative group bg-[#161d29] border border-[#2a3a4a] rounded-md p-2 flex flex-col items-center"><div class="text-sm font-bold cursor-pointer text-white">PA</div><div class="text-base font-normal">145</div><div class="absolute bottom-full mb-2 px-2 py-1 bg-black text-white text-xs rounded opacity-0 group-hover:opacity-100 transition text-center whitespace-pre z-50">Plate Appearances</div></div><div class="relative group bg-[#161d29] border border-[#2a3a4a] rounded-md p-2 flex flex-col items-center"><div class="text-sm font-bold cursor-pointer text-white">AB</div><div class="text-base font-normal">127</div><div class="absolute bottom-full mb-2 px-2 py-1 bg-black text-white text-xs rounded opacity-0 group-hover:opacity-100 transition text-center whitespace-pre z-50">At Bats</div></div><div class="relative group bg-[#161d29] border border-[#2a3a4a] rounded-md p-2 flex flex-col items-center"><div class="text-sm font-bold cursor-pointer text-white">SB</div><div class="text-base font-normal">4</div><div class="absolute bottom-full mb-2 px-2 py-1 bg-black text-white text-xs rounded opacity-0 group-hover:opacity-100 transition text-center whitespace-pre z-50">Stolen Bases</div></div><div class="relative group bg-[#161d29] border border-[#2a3a4a] rounded-md p-2 flex flex-col items-center"><div class="text-sm font-bold cursor-pointer text-white">CS</div><div class="text-base font-normal">3</div><div class="absolute bottom-full mb-2 px-2 py-1 bg-black text-white text-xs rounded opacity-0 group-hover:opacity-100 transition text-center whitespace-pre z-50">Caught Stealing</div></div><div class="relative group bg-[#161d29] border border-[#2a3a4a] rounded-md p-2 flex flex-col items-center"><div class="text-sm font-bold cursor-pointer text-white">GIDP</div><div class="text-base font-normal">3</div><div class="absolute bottom-full mb-2 px-2 py-1 bg-black text-white text-xs rounded opacity-0 group-hover:opacity-100 transition text-center whitespace-pre z-50">Grounded Into Double Plays</div></div></div></div><div class="mb-6"><div class="text-lg font-semibold mb-2 text-center">Pitching</div><div class="grid grid-cols-3 gap-3"><div class="relative group bg-[#161d29] border border-[#2a3a4a] rounded-md p-2 flex flex-col items-center"><div class="text-sm font-bold cursor-pointer text-white">ERA</div><div class="text-base font-normal">0.000</div><div class="absolute bottom-full mb-2 px-2 py-1 bg-black text-white text-xs rounded opacity-0 group-hover:opacity-100 transition text-center whitespace-pre z-50">Earned Run Average</div></div><div class="relative group bg-[#161d29] border border-[#2a3a4a] rounded-md p-2 flex flex-col items-center"><div class="text-sm font-bold cursor-pointer text-white">WHIP</div><div class="text-base font-normal">0.000</div><div class="absolute bottom-full mb-2 px-2 py-1 bg-black text-white text-xs rounded opacity-0 group-hover:opacity-100 transition text-center whitespace-pre z-50">Walks and Hits per Inning</div></div><div class="relative group bg-[#161d29] border border-[#2a3a4a] rounded-md p-2 flex flex-col items-center"><div class="text-sm font-bold cursor-pointer text-white">K/BB</div><div class="text-base font-normal">0.000</div><div class="absolute bottom-full mb-2 px-2 py-1 bg-black text-white text-xs rounded opacity-0 group-hover:opacity-100 transition text-center whitespace-pre z-50">Strikeout-to-Walk Ratio</div></div><div class="relative group bg-[#161d29] border border-[#2a3a4a] rounded-md p-2 flex flex-col items-center"><div class="text-sm font-bold cursor-pointer text-white">K/9</div><div class="text-base font-normal">0.000</div><div class="absolute bottom-full mb-2 px-2 py-1 bg-black text-white text-xs rounded opacity-0 group-hover:opacity-100 transition text-center whitespace-pre z-50">Strikeouts per 9 Innings</div></div><div class="relative group bg-[#161d29] border border-[#2a3a4a] rounded-md p-2 flex flex-col items-center"><div class="text-sm font-bold cursor-pointer text-white">H/9</div><div class="text-base font-normal">0.000</div><div class="absolute bottom-full mb-2 px-2 py-1 bg-black text-white text-xs rounded opacity-0 group-hover:opacity-100 transition text-center whitespace-pre z-50">Hits per 9 Innings</div></div><div class="relative group bg-[#161d29] border border-[#2a3a4a] rounded-md p-2 flex flex-col items-center"><div class="text-sm font-bold cursor-pointer text-white">BB/9</div><div class="text-base font-normal">0.000</div><div class="absolute bottom-full mb-2 px-2 py-1 bg-black text-white text-xs rounded opacity-0 group-hover:opacity-100 transition text-center whitespace-pre z-50">Walks per 9 Innings</div></div><div class="relative group bg-[#161d29] border border-[#2a3a4a] rounded-md p-2 flex flex-col items-center"><div class="text-sm font-bold cursor-pointer text-white">HR/9</div><div class="text-base font-normal">0.000</div><div class="absolute bottom-full mb-2 px-2 py-1 bg-black text-white text-xs rounded opacity-0 group-hover:opacity-100 transition text-center whitespace-pre z-50">Home Runs per 9 Innings</div></div><div class="relative group bg-[#161d29] border border-[#2a3a4a] rounded-md p-2 flex flex-col items-center"><div class="text-sm font-bold cursor-pointer text-white">IP</div><div class="text-base font-normal">0.0</div><div class="absolute bottom-full mb-2 px-2 py-1 bg-black text-white text-xs rounded opacity-0 group-hover:opacity-100 transition text-center whitespace-pre z-50">Innings Pitched</div></div><div class="relative group bg-[#161d29] border border-[#2a3a4a] rounded-md p-2 flex flex-col items-center"><div class="text-sm font-bold cursor-pointer text-white">K</div><div class="text-base font-normal">0</div><div class="absolute bottom-full mb-2 px-2 py-1 bg-black text-white text-xs rounded opacity-0 group-hover:opacity-100 transition text-center whitespace-pre z-50">Strikeouts</div></div><div class="relative group bg-[#161d29] border border-[#2a3a4a] rounded-md p-2 flex flex-col items-center"><div class="text-sm font-bold cursor-pointer text-white">BBP</div><div class="text-base font-normal">0</div><div class="absolute bottom-full mb-2 px-2 py-1 bg-black text-white text-xs rounded opacity-0 group-hover:opacity-100 transition text-center whitespace-pre z-50">Walks Issued (Pitching)</div></div><div class="relative group bg-[#161d29] border border-[#2a3a4a] rounded-md p-2 flex flex-col items-center"><div class="text-sm font-bold cursor-pointer text-white">HA</div><div class="text-base font-normal">0</div><div class="absolute bottom-full mb-2 px-2 py-1 bg-black text-white text-xs rounded opacity-0 group-hover:opacity-100 transition text-center whitespace-pre z-50">Hits Allowed</div></div><div class="relative group bg-[#161d29] border border-[#2a3a4a] rounded-md p-2 flex flex-col items-center"><div class="text-sm font-bold cursor-pointer text-white">HB</div><div class="text-base font-normal">0</div><div class="absolute bottom-full mb-2 px-2 py-1 bg-black text-white text-xs rounded opacity-0 group-hover:opacity-100 transition text-center whitespace-pre z-50">Hit Batters</div></div><div class="relative group bg-[#161d29] border border-[#2a3a4a] rounded-md p-2 flex flex-col items-center"><div class="text-sm font-bold cursor-pointer text-white">ER</div><div class="text-base font-normal">0</div><div class="absolute bottom-full mb-2 px-2 py-1 bg-black text-white text-xs rounded opacity-0 group-hover:opacity-100 transition text-center whitespace-pre z-50">Earned Runs</div></div><div class="relative group bg-[#161d29] border border-[#2a3a4a] rounded-md p-2 flex flex-col items-center"><div class="text-sm font-bold cursor-pointer text-white">W</div><div class="text-base font-normal">0</div><div class="absolute bottom-full mb-2 px-2 py-1 bg-black text-white text-xs rounded opacity-0 group-hover:opacity-100 transition text-center whitespace-pre z-50">Wins</div></div><div class="relative group bg-[#161d29] border border-[#2a3a4a] rounded-md p-2 flex flex-col items-center"><div class="text-sm font-bold cursor-pointer text-white">L</div><div class="text-base font-normal">0</div><div class="absolute bottom-full mb-2 px-2 py-1 bg-black text-white text-xs rounded opacity-0 group-hover:opacity-100 transition text-center whitespace-pre z-50">Losses</div></div><div class="relative group bg-[#161d29] border border-[#2a3a4a] rounded-md p-2 flex flex-col items-center"><div class="text-sm font-bold cursor-pointer text-white">QS</div><div class="text-base font-normal">0</div><div class="absolute bottom-full mb-2 px-2 py-1 bg-black text-white text-xs rounded opacity-0 group-hover:opacity-100 transition text-center whitespace-pre z-50">Quality Starts</div></div><div class="relative group bg-[#161d29] border border-[#2a3a4a] rounded-md p-2 flex flex-col items-center"><div class="text-sm font-bold cursor-pointer text-white">SV</div><div class="text-base font-normal">0</div><div class="absolute bottom-full mb-2 px-2 py-1 bg-black text-white text-xs rounded opacity-0 group-hover:opacity-100 transition text-center whitespace-pre z-50">Saves</div></div><div class="relative group bg-[#161d29] border border-[#2a3a4a] rounded-md p-2 flex flex-col items-center"><div class="text-sm font-bold cursor-pointer text-white">BS</div><div class="text-base font-normal">0</div><div class="absolute bottom-full mb-2 px-2 py-1 bg-black text-white text-xs rounded opacity-0 group-hover:opacity-100 transition text-center whitespace-pre z-50">Blown Saves</div></div><div class="relative group bg-[#161d29] border border-[#2a3a4a] rounded-md p-2 flex flex-col items-center"><div class="text-sm font-bold cursor-pointer text-white">G</div><div class="text-base font-normal">0</div><div class="absolute bottom-full mb-2 px-2 py-1 bg-black text-white text-xs rounded opacity-0 group-hover:opacity-100 transition text-center whitespace-pre z-50">Games Pitched</div></div><div class="relative group bg-[#161d29] border border-[#2a3a4a] rounded-md p-2 flex flex-col items-center"><div class="text-sm font-bold cursor-pointer text-white">GF</div><div class="text-base font-normal">0</div><div class="absolute bottom-full mb-2 px-2 py-1 bg-black text-white text-xs rounded opacity-0 group-hover:opacity-100 transition text-center whitespace-pre z-50">Games Finished</div></div><div class="relative group bg-[#161d29] border border-[#2a3a4a] rounded-md p-2 flex flex-col items-center"><div class="text-sm font-bold cursor-pointer text-white">CG</div><div class="text-base font-normal">0</div><div class="absolute bottom-full mb-2 px-2 py-1 bg-black text-white text-xs rounded opacity-0 group-hover:opacity-100 transition text-center whitespace-pre z-50">Complete Games</div></div><div class="relative group bg-[#161d29] border border-[#2a3a4a] rounded-md p-2 flex flex-col items-center"><div class="text-sm font-bold cursor-pointer text-white">SHO</div><div class="text-base font-normal">0</div><div class="absolute bottom-full mb-2 px-2 py-1 bg-black text-white text-xs rounded opacity-0 group-hover:opacity-100 transition text-center whitespace-pre z-50">Shutouts</div></div><div class="relative group bg-[#161d29] border border-[#2a3a4a] rounded-md p-2 flex flex-col items-center"><div class="text-sm font-bold cursor-pointer text-white">NH</div><div class="text-base font-normal">0</div><div class="absolute bottom-full mb-2 px-2 py-1 bg-black text-white text-xs rounded opacity-0 group-hover:opacity-100 transition text-center whitespace-pre z-50">No Hitters</div></div></div></div><div class="mb-6"><div class="text-lg font-semibold mb-2 text-center">Defense</div><div class="grid grid-cols-3 gap-3"><div class="relative group bg-[#161d29] border border-[#2a3a4a] rounded-md p-2 flex flex-col items-center"><div class="text-sm font-bold cursor-pointer text-white">E</div><div class="text-base font-normal">0</div><div class="absolute bottom-full mb-2 px-2 py-1 bg-black text-white text-xs rounded opacity-0 group-hover:opacity-100 transition text-center whitespace-pre z-50">Errors</div></div><div class="relative group bg-[#161d29] border border-[#2a3a4a] rounded-md p-2 flex flex-col items-center"><div class="text-sm font-bold cursor-pointer text-white">A</div><div class="text-base font-normal">29</div><div class="absolute bottom-full mb-2 px-2 py-1 bg-black text-white text-xs rounded opacity-0 group-hover:opacity-100 transition text-center whitespace-pre z-50">Assists</div></div><div class="relative group bg-[#161d29] border border-[#2a3a4a] rounded-md p-2 flex flex-col items-center"><div class="text-sm font-bold cursor-pointer text-white">PO</div><div class="text-base font-normal">331</div><div class="absolute bottom-full mb-2 px-2 py-1 bg-black text-white text-xs rounded opacity-0 group-hover:opacity-100 transition text-center whitespace-pre z-50">Putouts</div></div><div class="relative group bg-[#161d29] border border-[#2a3a4a] rounded-md p-2 flex flex-col items-center"><div class="text-sm font-bold cursor-pointer text-white">DP</div><div class="text-base font-normal">1</div><div class="absolute bottom-full mb-2 px-2 py-1 bg-black text-white text-xs rounded opacity-0 group-hover:opacity-100 transition text-center whitespace-pre z-50">Double Plays</div></div></div></div></div><div class="mt-8"><div class="flex items-center justify-between mb-2"><span class="text-xl font-bold">Recent Events</span><select class="bg-[#1c2a3a] text-white px-2 py-1 rounded text-sm"><option value="2">Season 2</option><option value="1">Season 1</option></select></div><div class="bg-[#1c2a3a] rounded-xl p-3 max-h-60 overflow-y-auto text-sm space-y-1 mb-4"><div>🧩 Season 2, Regular Season, Day 46: <span class="underline cursor-pointer text-blue-400 hover:text-blue-300">Jamie McBride</span> gained +10 Speed.</div><div>🧩 Season 1, Postseason Round 3, Day 251: <span class="underline cursor-pointer text-blue-400 hover:text-blue-300">Jamie McBride</span> gained +30 Dexterity.</div><div>🧩 Season 1, Postseason Round 1, Day 242: <span class="underline cursor-pointer text-blue-400 hover:text-blue-300">Jamie McBride</span> gained +15 Contact.</div><div>📦 Season 1, Regular Season, Day 228: <span class="underline cursor-pointer text-blue-400 hover:text-blue-300">Jamie McBride</span> received a 💍 Ring Delivery.</div><div>📦 Season 1, Regular Season, Day 194: <span class="underline cursor-pointer text-blue-400 hover:text-blue-300">Jamie McBride</span> received a 🧢 Cap Delivery.</div><div>📦 Season 1, Regular Season, Day 80: <span class="underline cursor-pointer text-blue-400 hover:text-blue-300">Jamie McBride</span> received a 👕 T-Shirt Delivery.</div><div>📦 Season 1, Regular Season, Day 56: <span class="underline cursor-pointer text-blue-400 hover:text-blue-300">Jamie McBride</span> received a 👟 Sneakers Delivery.</div><div>📦 Season 1, Regular Season, Day 24: <span class="underline cursor-pointer text-blue-400 hover:text-blue-300">Jamie McBride</span> received a 🧤 Gloves Delivery.</div></div></div></div></main><script src="/_next/static/chunks/webpack-bbe26068de7d4bca.js" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[8319,[\"148\",\"static/chunks/148-a28f96f07fb7334b.js\",\"851\",\"static/chunks/851-c6952f3282869f27.js\",\"874\",\"static/chunks/874-07fd613b034f939c.js\",\"177\",\"static/chunks/app/layout-249c622807ca6ae3.js\"],\"default\"]\n3:I[9118,[\"148\",\"static/chunks/148-a28f96f07fb7334b.js\",\"851\",\"static/chunks/851-c6952f3282869f27.js\",\"874\",\"static/chunks/874-07fd613b034f939c.js\",\"177\",\"static/chunks/app/layout-249c622807ca6ae3.js\"],\"default\"]\n4:I[1158,[\"148\",\"static/chunks/148-a28f96f07fb7334b.js\",\"851\",\"static/chunks/851-c6952f3282869f27.js\",\"874\",\"static/chunks/874-07fd613b034f939c.js\",\"177\",\"static/chunks/app/layout-249c622807ca6ae3.js\"],\"default\"]\n5:I[7555,[],\"\"]\n6:I[1295,[],\"\"]\n8:I[9665,[],\"MetadataBoundary\"]\na:I[9665,[],\"OutletBoundary\"]\nd:I[4911,[],\"AsyncMetadataOutlet\"]\nf:I[9665,[],\"ViewportBoundary\"]\n11:I[6614,[],\"\"]\n12:I[2802,[\"148\",\"static/chunks/148-a28f96f07fb7334b.js\",\"239\",\"static/chunks/app/player/%5Bplayerid%5D/page-552b387be00ed078.js\"],\"default\"]\n13:\"$Sreact.suspense\"\n14:I[4911,[],\"AsyncMetadata\"]\n:HL[\"/_next/static/media/569ce4b8f30dc480-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n:HL[\"/_next/static/media/93f479601ee12b01-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n:HL[\"/_next/static/css/c0953f4fe994bba5.css\",\"style\"]\n"])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"iigAj2wS6KNkbs_ctfjf_\",\"p\":\"\",\"c\":[\"\",\"player\",\"68412f4654a7fbd413387442\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"player\",{\"children\":[[\"playerid\",\"68412f4654a7fbd413387442\",\"d\"],{\"children\":[\"__PAGE__\",{}]}]}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/c0953f4fe994bba5.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"en\",\"children\":[\"$\",\"body\",null,{\"className\":\"__variable_5cfdac __variable_9a8899 min-h-screen\",\"children\":[\"$\",\"$L2\",null,{\"children\":[[\"$\",\"$L3\",null,{}],[\"$\",\"$L4\",null,{}],[\"$\",\"main\",null,{\"className\":\"mt-16\",\"children\":[\"$\",\"$L5\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L6\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":404}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}]]}]}]}]]}],{\"children\":[\"player\",[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$L5\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L6\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[[\"playerid\",\"68412f4654a7fbd413387442\",\"d\"],[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$L5\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L6\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[\"$L7\",[\"$\",\"$L8\",null,{\"children\":\"$L9\"}],null,[\"$\",\"$La\",null,{\"children\":[\"$Lb\",\"$Lc\",[\"$\",\"$Ld\",null,{\"promise\":\"$@e\"}]]}]]}],{},null,false]},null,false]},null,false]},null,false],[\"$\",\"$1\",\"h\",{\"children\":[null,[\"$\",\"$1\",\"AhtzouYUqKR_OXjsq0Il8\",{\"children\":[[\"$\",\"$Lf\",null,{\"children\":\"$L10\"}],[\"$\",\"meta\",null,{\"name\":\"next-size-adjust\",\"content\":\"\"}]]}],null]}],false]],\"m\":\"$undefined\",\"G\":[\"$11\",\"$undefined\"],\"s\":false,\"S\":false}\n"])</script><script>self.__next_f.push([1,"7:[\"$\",\"$L12\",null,{\"playerid\":\"68412f4654a7fbd413387442\"}]\n9:[\"$\",\"$13\",null,{\"fallback\":null,\"children\":[\"$\",\"$L14\",null,{\"promise\":\"$@15\"}]}]\nc:null\n15:{\"metadata\":[[\"$\",\"title\",\"0\",{\"children\":\"MMOLB\"}],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"Massively Multiplayer Online League Baseball\"}],[\"$\",\"link\",\"2\",{\"rel\":\"icon\",\"href\":\"/favicon.ico\",\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}]],\"error\":null,\"digest\":\"$undefined\"}\ne:{\"metadata\":\"$15:metadata\",\"error\":null,\"digest\":\"$undefined\"}\n10:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}]]\nb:null\n"])</script><next-route-announcer style="position: absolute;"></next-route-announcer></body>